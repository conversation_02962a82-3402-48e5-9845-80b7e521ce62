// Interfaces para estatísticas salvas no Firestore

export interface UserStatistics {
  userId: string;
  totalMessages: number;
  totalWords: number;
  totalTokens: number;
  totalCost: number;
  averageResponseTime: number;
  lastUpdated: number;
  createdAt: number;
}

export interface ChatStatistics {
  chatId: string;
  userId: string;
  chatName: string;
  totalMessages: number;
  totalWords: number;
  totalTokens: number;
  totalCost: number;
  averageResponseTime: number;
  lastUpdated: number;
  createdAt: number;
}

export interface DailyStatistics {
  date: string; // YYYY-MM-DD
  userId: string;
  messages: number;
  words: number;
  tokens: number;
  cost: number;
  averageResponseTime: number;
  modelsUsed: { [modelId: string]: number }; // contagem por modelo
  lastUpdated: number;
}

export interface ModelUsageStatistics {
  modelId: string;
  userId: string;
  totalMessages: number;
  totalTokens: number;
  totalCost: number;
  averageResponseTime: number;
  lastUsed: number;
  firstUsed: number;
  lastUpdated: number;
}

export interface WeeklyStatistics {
  week: string; // YYYY-WW (ano-semana)
  userId: string;
  messages: number;
  words: number;
  tokens: number;
  cost: number;
  averageResponseTime: number;
  modelsUsed: { [modelId: string]: number };
  lastUpdated: number;
}

export interface MonthlyStatistics {
  month: string; // YYYY-MM
  userId: string;
  messages: number;
  words: number;
  tokens: number;
  cost: number;
  averageResponseTime: number;
  modelsUsed: { [modelId: string]: number };
  lastUpdated: number;
}

// Interface para batch updates de estatísticas
export interface StatisticsUpdate {
  userStats: Partial<UserStatistics>;
  chatStats: Partial<ChatStatistics>;
  dailyStats: Partial<DailyStatistics>;
  modelStats: Partial<ModelUsageStatistics>;
  weeklyStats: Partial<WeeklyStatistics>;
  monthlyStats: Partial<MonthlyStatistics>;
}
