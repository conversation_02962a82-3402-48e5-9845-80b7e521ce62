import { doc, setDoc, getDoc, updateDoc, increment, collection, getDocs, query, orderBy, limit } from 'firebase/firestore';
import { db } from '@/lib/firebase';

interface SearchTerm {
  term: string;
  count: number;
  lastUsed: number;
  firstUsed: number;
}

interface UserSearchHistory {
  userId: string;
  searches: SearchTerm[];
  totalSearches: number;
  lastSearchAt: number;
}

interface ModelSelectionEvent {
  modelId: string;
  modelName: string;
  searchTerm?: string;
  timestamp: number;
  userId: string;
}

class SearchAnalyticsService {
  // Rastrear termo de busca
  async trackSearchTerm(userId: string, searchTerm: string): Promise<void> {
    if (!searchTerm.trim() || !userId) return;

    try {
      const term = searchTerm.toLowerCase().trim();
      
      // Atualizar estatísticas globais
      await this.updateGlobalSearchStats(term);
      
      // Atualizar histórico do usuário
      await this.updateUserSearchHistory(userId, term);
      
    } catch (error) {
      console.error('Error tracking search term:', error);
      // Não propagar o erro para não afetar a funcionalidade principal
    }
  }

  // Rastrear seleção de modelo
  async trackModelSelection(userId: string, modelId: string, modelName: string, searchTerm?: string): Promise<void> {
    if (!userId || !modelId) return;

    try {
      const selectionEvent: ModelSelectionEvent = {
        modelId,
        modelName,
        searchTerm: searchTerm?.trim() || undefined,
        timestamp: Date.now(),
        userId
      };

      // Salvar evento de seleção
      const eventRef = doc(collection(db, 'search_analytics', 'model_selections', 'events'));
      await setDoc(eventRef, selectionEvent);

      // Atualizar estatísticas do modelo
      await this.updateModelStats(modelId, modelName);

    } catch (error) {
      console.error('Error tracking model selection:', error);
      // Não propagar o erro para não afetar a funcionalidade principal
    }
  }

  // Atualizar estatísticas globais de busca
  private async updateGlobalSearchStats(term: string): Promise<void> {
    try {
      const termRef = doc(db, 'search_analytics', 'global', 'terms', term);
      const termDoc = await getDoc(termRef);

      if (termDoc.exists()) {
        await updateDoc(termRef, {
          count: increment(1),
          lastUsed: Date.now()
        });
      } else {
        await setDoc(termRef, {
          term,
          count: 1,
          firstUsed: Date.now(),
          lastUsed: Date.now()
        });
      }
    } catch (error) {
      console.error('Error updating global search stats:', error);
    }
  }

  // Atualizar histórico de busca do usuário
  private async updateUserSearchHistory(userId: string, term: string): Promise<void> {
    try {
      const userRef = doc(db, 'search_analytics', 'users', userId);
      const userDoc = await getDoc(userRef);

      if (userDoc.exists()) {
        const data = userDoc.data() as UserSearchHistory;
        const existingTerm = data.searches.find(s => s.term === term);

        if (existingTerm) {
          existingTerm.count++;
          existingTerm.lastUsed = Date.now();
        } else {
          data.searches.push({
            term,
            count: 1,
            firstUsed: Date.now(),
            lastUsed: Date.now()
          });
        }

        // Manter apenas os 50 termos mais recentes
        data.searches = data.searches
          .sort((a, b) => b.lastUsed - a.lastUsed)
          .slice(0, 50);

        await updateDoc(userRef, {
          searches: data.searches,
          totalSearches: increment(1),
          lastSearchAt: Date.now()
        });
      } else {
        const newUserHistory: UserSearchHistory = {
          userId,
          searches: [{
            term,
            count: 1,
            firstUsed: Date.now(),
            lastUsed: Date.now()
          }],
          totalSearches: 1,
          lastSearchAt: Date.now()
        };

        await setDoc(userRef, newUserHistory);
      }
    } catch (error) {
      console.error('Error updating user search history:', error);
    }
  }

  // Atualizar estatísticas do modelo
  private async updateModelStats(modelId: string, modelName: string): Promise<void> {
    try {
      const modelRef = doc(db, 'search_analytics', 'models', modelId);
      const modelDoc = await getDoc(modelRef);

      if (modelDoc.exists()) {
        await updateDoc(modelRef, {
          selectionCount: increment(1),
          lastSelected: Date.now()
        });
      } else {
        await setDoc(modelRef, {
          modelId,
          modelName,
          selectionCount: 1,
          firstSelected: Date.now(),
          lastSelected: Date.now()
        });
      }
    } catch (error) {
      console.error('Error updating model stats:', error);
    }
  }

  // Obter termos de busca populares globalmente
  async getPopularSearchTerms(limitCount: number = 10): Promise<SearchTerm[]> {
    try {
      const termsRef = collection(db, 'search_analytics', 'global', 'terms');
      const q = query(termsRef, orderBy('count', 'desc'), limit(limitCount));
      const snapshot = await getDocs(q);

      const terms: SearchTerm[] = [];
      snapshot.forEach(doc => {
        terms.push(doc.data() as SearchTerm);
      });

      return terms;
    } catch (error) {
      console.error('Error getting popular search terms:', error);
      return [];
    }
  }

  // Obter histórico de busca do usuário
  async getUserSearchHistory(userId: string): Promise<SearchTerm[]> {
    try {
      const userRef = doc(db, 'search_analytics', 'users', userId);
      const userDoc = await getDoc(userRef);

      if (userDoc.exists()) {
        const data = userDoc.data() as UserSearchHistory;
        return data.searches.sort((a, b) => b.lastUsed - a.lastUsed);
      }

      return [];
    } catch (error) {
      console.error('Error getting user search history:', error);
      return [];
    }
  }

  // Obter sugestões de busca baseadas no histórico do usuário
  async getSearchSuggestions(userId: string, currentTerm: string, limitCount: number = 5): Promise<string[]> {
    try {
      const userHistory = await this.getUserSearchHistory(userId);
      const popularTerms = await this.getPopularSearchTerms(20);

      // Combinar histórico do usuário com termos populares
      const allTerms = [
        ...userHistory.map(s => s.term),
        ...popularTerms.map(s => s.term)
      ];

      // Filtrar termos que começam com o termo atual
      const suggestions = allTerms
        .filter(term => 
          term.toLowerCase().startsWith(currentTerm.toLowerCase()) && 
          term.toLowerCase() !== currentTerm.toLowerCase()
        )
        .slice(0, limitCount);

      // Remover duplicatas mantendo a ordem
      return Array.from(new Set(suggestions));
    } catch (error) {
      console.error('Error getting search suggestions:', error);
      return [];
    }
  }

  // Obter estatísticas de uso do usuário
  async getUserAnalytics(userId: string): Promise<{
    totalSearches: number;
    uniqueTerms: number;
    mostUsedTerm?: SearchTerm;
    recentSearches: SearchTerm[];
    lastSearchAt?: number;
  }> {
    try {
      const userHistory = await this.getUserSearchHistory(userId);
      
      if (userHistory.length === 0) {
        return {
          totalSearches: 0,
          uniqueTerms: 0,
          recentSearches: []
        };
      }

      const totalSearches = userHistory.reduce((sum, term) => sum + term.count, 0);
      const mostUsedTerm = userHistory.reduce((max, term) => 
        term.count > max.count ? term : max
      );

      return {
        totalSearches,
        uniqueTerms: userHistory.length,
        mostUsedTerm,
        recentSearches: userHistory.slice(0, 10),
        lastSearchAt: Math.max(...userHistory.map(s => s.lastUsed))
      };
    } catch (error) {
      console.error('Error getting user analytics:', error);
      return {
        totalSearches: 0,
        uniqueTerms: 0,
        recentSearches: []
      };
    }
  }

  // Limpar histórico de busca do usuário
  async clearUserSearchHistory(userId: string): Promise<void> {
    try {
      const userRef = doc(db, 'search_analytics', 'users', userId);
      await setDoc(userRef, {
        userId,
        searches: [],
        totalSearches: 0,
        lastSearchAt: Date.now()
      });
    } catch (error) {
      console.error('Error clearing user search history:', error);
      throw error;
    }
  }
}

export const searchAnalyticsService = new SearchAnalyticsService();
