﻿import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';
import { getFunctions } from 'firebase/functions';

// Configuração do Firebase - novo app criado - ATUALIZADO
const firebaseConfig = {
  apiKey: "AIzaSyA4ojPmlKBkDDl2hcfNPDXG23tEgolgCv8",
  authDomain: "rafthor-0001.firebaseapp.com",
  projectId: "rafthor-0001",
  storageBucket: "rafthor-0001.firebasestorage.app",
  messagingSenderId: "863587500028",
  appId: "1:863587500028:web:ea161ddd3a1a024a7f3c79"
};

// Verificar se a configuração está correta
if (!firebaseConfig.apiKey || firebaseConfig.apiKey.length < 30) {
  throw new Error('Firebase API Key inválida ou não configurada');
}

// Inicializar Firebase
const app = initializeApp(firebaseConfig);

// Inicializar serviços
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);
export const functions = getFunctions(app);

export default app;
