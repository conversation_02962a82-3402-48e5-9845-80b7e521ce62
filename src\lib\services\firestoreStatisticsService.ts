import { db } from '@/lib/firebase';
import { 
  doc, 
  setDoc, 
  getDoc, 
  updateDoc, 
  increment, 
  serverTimestamp,
  writeBatch,
  collection
} from 'firebase/firestore';
import { 
  UserStatistics, 
  ChatStatistics, 
  DailyStatistics, 
  ModelUsageStatistics,
  WeeklyStatistics,
  MonthlyStatistics
} from '@/lib/types/firestoreStatistics';
import { ChatMessage } from '@/lib/types/chat';

class FirestoreStatisticsService {
  /**
   * Atualiza todas as estatísticas após uma nova mensagem da IA
   */
  async updateStatisticsAfterMessage(
    userId: string,
    chatId: string,
    chatName: string,
    message: ChatMessage,
    modelId: string
  ): Promise<void> {
    if (message.role !== 'assistant' || !message.usage) {
      return; // Só processa mensagens da IA com dados de uso
    }

    const batch = writeBatch(db);
    const now = Date.now();
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    const thisWeek = this.getWeekString(new Date());
    const thisMonth = new Date().toISOString().substring(0, 7); // YYYY-MM

    // Calcular palavras na mensagem
    const wordCount = this.countWords(message.content);

    try {
      // 1. Atualizar estatísticas do usuário
      await this.updateUserStatistics(batch, userId, {
        totalMessages: 1,
        totalWords: wordCount,
        totalTokens: message.usage.total_tokens,
        totalCost: message.usage.cost,
        responseTime: message.responseTime || 0
      });

      // 2. Atualizar estatísticas do chat
      await this.updateChatStatistics(batch, userId, chatId, chatName, {
        totalMessages: 1,
        totalWords: wordCount,
        totalTokens: message.usage.total_tokens,
        totalCost: message.usage.cost,
        responseTime: message.responseTime || 0
      });

      // 3. Atualizar estatísticas diárias
      await this.updateDailyStatistics(batch, userId, today, {
        messages: 1,
        words: wordCount,
        tokens: message.usage.total_tokens,
        cost: message.usage.cost,
        responseTime: message.responseTime || 0,
        modelId
      });

      // 4. Atualizar estatísticas do modelo
      await this.updateModelStatistics(batch, userId, modelId, {
        totalMessages: 1,
        totalTokens: message.usage.total_tokens,
        totalCost: message.usage.cost,
        responseTime: message.responseTime || 0
      });

      // 5. Atualizar estatísticas semanais
      await this.updateWeeklyStatistics(batch, userId, thisWeek, {
        messages: 1,
        words: wordCount,
        tokens: message.usage.total_tokens,
        cost: message.usage.cost,
        responseTime: message.responseTime || 0,
        modelId
      });

      // 6. Atualizar estatísticas mensais
      await this.updateMonthlyStatistics(batch, userId, thisMonth, {
        messages: 1,
        words: wordCount,
        tokens: message.usage.total_tokens,
        cost: message.usage.cost,
        responseTime: message.responseTime || 0,
        modelId
      });

      // Executar todas as atualizações em batch
      await batch.commit();
      console.log('Estatísticas atualizadas com sucesso');

    } catch (error) {
      console.error('Erro ao atualizar estatísticas:', error);
      throw error;
    }
  }

  /**
   * Atualiza estatísticas do usuário
   */
  private async updateUserStatistics(
    batch: any,
    userId: string,
    updates: {
      totalMessages: number;
      totalWords: number;
      totalTokens: number;
      totalCost: number;
      responseTime: number;
    }
  ): Promise<void> {
    const userStatsRef = doc(db, 'statistics', 'users', 'data', userId);
    const userStatsDoc = await getDoc(userStatsRef);

    if (userStatsDoc.exists()) {
      const currentData = userStatsDoc.data() as UserStatistics;
      const newTotalMessages = currentData.totalMessages + updates.totalMessages;
      const newAverageResponseTime = 
        ((currentData.averageResponseTime * currentData.totalMessages) + updates.responseTime) / newTotalMessages;

      batch.update(userStatsRef, {
        totalMessages: increment(updates.totalMessages),
        totalWords: increment(updates.totalWords),
        totalTokens: increment(updates.totalTokens),
        totalCost: increment(updates.totalCost),
        averageResponseTime: newAverageResponseTime,
        lastUpdated: Date.now()
      });
    } else {
      batch.set(userStatsRef, {
        userId,
        totalMessages: updates.totalMessages,
        totalWords: updates.totalWords,
        totalTokens: updates.totalTokens,
        totalCost: updates.totalCost,
        averageResponseTime: updates.responseTime,
        lastUpdated: Date.now(),
        createdAt: Date.now()
      });
    }
  }

  /**
   * Atualiza estatísticas do chat
   */
  private async updateChatStatistics(
    batch: any,
    userId: string,
    chatId: string,
    chatName: string,
    updates: {
      totalMessages: number;
      totalWords: number;
      totalTokens: number;
      totalCost: number;
      responseTime: number;
    }
  ): Promise<void> {
    const chatStatsRef = doc(db, 'statistics', 'chats', 'data', `${userId}_${chatId}`);
    const chatStatsDoc = await getDoc(chatStatsRef);

    if (chatStatsDoc.exists()) {
      const currentData = chatStatsDoc.data() as ChatStatistics;
      const newTotalMessages = currentData.totalMessages + updates.totalMessages;
      const newAverageResponseTime = 
        ((currentData.averageResponseTime * currentData.totalMessages) + updates.responseTime) / newTotalMessages;

      batch.update(chatStatsRef, {
        totalMessages: increment(updates.totalMessages),
        totalWords: increment(updates.totalWords),
        totalTokens: increment(updates.totalTokens),
        totalCost: increment(updates.totalCost),
        averageResponseTime: newAverageResponseTime,
        lastUpdated: Date.now()
      });
    } else {
      batch.set(chatStatsRef, {
        chatId,
        userId,
        chatName,
        totalMessages: updates.totalMessages,
        totalWords: updates.totalWords,
        totalTokens: updates.totalTokens,
        totalCost: updates.totalCost,
        averageResponseTime: updates.responseTime,
        lastUpdated: Date.now(),
        createdAt: Date.now()
      });
    }
  }

  /**
   * Conta palavras em um texto
   */
  private countWords(text: string): number {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * Gera string da semana no formato YYYY-WW
   */
  private getWeekString(date: Date): string {
    const year = date.getFullYear();
    const startOfYear = new Date(year, 0, 1);
    const days = Math.floor((date.getTime() - startOfYear.getTime()) / (24 * 60 * 60 * 1000));
    const week = Math.ceil((days + startOfYear.getDay() + 1) / 7);
    return `${year}-${week.toString().padStart(2, '0')}`;
  }

  /**
   * Atualiza estatísticas diárias
   */
  private async updateDailyStatistics(
    batch: any,
    userId: string,
    date: string,
    updates: {
      messages: number;
      words: number;
      tokens: number;
      cost: number;
      responseTime: number;
      modelId: string;
    }
  ): Promise<void> {
    const dailyStatsRef = doc(db, 'statistics', 'daily', 'data', `${userId}_${date}`);
    const dailyStatsDoc = await getDoc(dailyStatsRef);

    if (dailyStatsDoc.exists()) {
      const currentData = dailyStatsDoc.data() as DailyStatistics;
      const newTotalMessages = currentData.messages + updates.messages;
      const newAverageResponseTime =
        ((currentData.averageResponseTime * currentData.messages) + updates.responseTime) / newTotalMessages;

      const modelsUsed = { ...currentData.modelsUsed };
      modelsUsed[updates.modelId] = (modelsUsed[updates.modelId] || 0) + 1;

      batch.update(dailyStatsRef, {
        messages: increment(updates.messages),
        words: increment(updates.words),
        tokens: increment(updates.tokens),
        cost: increment(updates.cost),
        averageResponseTime: newAverageResponseTime,
        modelsUsed,
        lastUpdated: Date.now()
      });
    } else {
      batch.set(dailyStatsRef, {
        date,
        userId,
        messages: updates.messages,
        words: updates.words,
        tokens: updates.tokens,
        cost: updates.cost,
        averageResponseTime: updates.responseTime,
        modelsUsed: { [updates.modelId]: 1 },
        lastUpdated: Date.now()
      });
    }
  }

  /**
   * Atualiza estatísticas do modelo
   */
  private async updateModelStatistics(
    batch: any,
    userId: string,
    modelId: string,
    updates: {
      totalMessages: number;
      totalTokens: number;
      totalCost: number;
      responseTime: number;
    }
  ): Promise<void> {
    const modelStatsRef = doc(db, 'statistics', 'models', 'data', `${userId}_${modelId}`);
    const modelStatsDoc = await getDoc(modelStatsRef);
    const now = Date.now();

    if (modelStatsDoc.exists()) {
      const currentData = modelStatsDoc.data() as ModelUsageStatistics;
      const newTotalMessages = currentData.totalMessages + updates.totalMessages;
      const newAverageResponseTime =
        ((currentData.averageResponseTime * currentData.totalMessages) + updates.responseTime) / newTotalMessages;

      batch.update(modelStatsRef, {
        totalMessages: increment(updates.totalMessages),
        totalTokens: increment(updates.totalTokens),
        totalCost: increment(updates.totalCost),
        averageResponseTime: newAverageResponseTime,
        lastUsed: now,
        lastUpdated: now
      });
    } else {
      batch.set(modelStatsRef, {
        modelId,
        userId,
        totalMessages: updates.totalMessages,
        totalTokens: updates.totalTokens,
        totalCost: updates.totalCost,
        averageResponseTime: updates.responseTime,
        lastUsed: now,
        firstUsed: now,
        lastUpdated: now
      });
    }
  }

  /**
   * Atualiza estatísticas semanais
   */
  private async updateWeeklyStatistics(
    batch: any,
    userId: string,
    week: string,
    updates: {
      messages: number;
      words: number;
      tokens: number;
      cost: number;
      responseTime: number;
      modelId: string;
    }
  ): Promise<void> {
    const weeklyStatsRef = doc(db, 'statistics', 'weekly', 'data', `${userId}_${week}`);
    const weeklyStatsDoc = await getDoc(weeklyStatsRef);

    if (weeklyStatsDoc.exists()) {
      const currentData = weeklyStatsDoc.data() as WeeklyStatistics;
      const newTotalMessages = currentData.messages + updates.messages;
      const newAverageResponseTime =
        ((currentData.averageResponseTime * currentData.messages) + updates.responseTime) / newTotalMessages;

      const modelsUsed = { ...currentData.modelsUsed };
      modelsUsed[updates.modelId] = (modelsUsed[updates.modelId] || 0) + 1;

      batch.update(weeklyStatsRef, {
        messages: increment(updates.messages),
        words: increment(updates.words),
        tokens: increment(updates.tokens),
        cost: increment(updates.cost),
        averageResponseTime: newAverageResponseTime,
        modelsUsed,
        lastUpdated: Date.now()
      });
    } else {
      batch.set(weeklyStatsRef, {
        week,
        userId,
        messages: updates.messages,
        words: updates.words,
        tokens: updates.tokens,
        cost: updates.cost,
        averageResponseTime: updates.responseTime,
        modelsUsed: { [updates.modelId]: 1 },
        lastUpdated: Date.now()
      });
    }
  }

  /**
   * Atualiza estatísticas mensais
   */
  private async updateMonthlyStatistics(
    batch: any,
    userId: string,
    month: string,
    updates: {
      messages: number;
      words: number;
      tokens: number;
      cost: number;
      responseTime: number;
      modelId: string;
    }
  ): Promise<void> {
    const monthlyStatsRef = doc(db, 'statistics', 'monthly', 'data', `${userId}_${month}`);
    const monthlyStatsDoc = await getDoc(monthlyStatsRef);

    if (monthlyStatsDoc.exists()) {
      const currentData = monthlyStatsDoc.data() as MonthlyStatistics;
      const newTotalMessages = currentData.messages + updates.messages;
      const newAverageResponseTime =
        ((currentData.averageResponseTime * currentData.messages) + updates.responseTime) / newTotalMessages;

      const modelsUsed = { ...currentData.modelsUsed };
      modelsUsed[updates.modelId] = (modelsUsed[updates.modelId] || 0) + 1;

      batch.update(monthlyStatsRef, {
        messages: increment(updates.messages),
        words: increment(updates.words),
        tokens: increment(updates.tokens),
        cost: increment(updates.cost),
        averageResponseTime: newAverageResponseTime,
        modelsUsed,
        lastUpdated: Date.now()
      });
    } else {
      batch.set(monthlyStatsRef, {
        month,
        userId,
        messages: updates.messages,
        words: updates.words,
        tokens: updates.tokens,
        cost: updates.cost,
        averageResponseTime: updates.responseTime,
        modelsUsed: { [updates.modelId]: 1 },
        lastUpdated: Date.now()
      });
    }
  }

  /**
   * Recupera estatísticas do usuário
   */
  async getUserStatistics(userId: string): Promise<UserStatistics | null> {
    try {
      const userStatsRef = doc(db, 'statistics', 'users', 'data', userId);
      const userStatsDoc = await getDoc(userStatsRef);

      if (userStatsDoc.exists()) {
        return userStatsDoc.data() as UserStatistics;
      }
      return null;
    } catch (error) {
      console.error('Erro ao buscar estatísticas do usuário:', error);
      return null;
    }
  }

  /**
   * Recupera estatísticas de um chat específico
   */
  async getChatStatistics(userId: string, chatId: string): Promise<ChatStatistics | null> {
    try {
      const chatStatsRef = doc(db, 'statistics', 'chats', 'data', `${userId}_${chatId}`);
      const chatStatsDoc = await getDoc(chatStatsRef);

      if (chatStatsDoc.exists()) {
        return chatStatsDoc.data() as ChatStatistics;
      }
      return null;
    } catch (error) {
      console.error('Erro ao buscar estatísticas do chat:', error);
      return null;
    }
  }
}

export const firestoreStatisticsService = new FirestoreStatisticsService();
