"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@ungap";
exports.ids = ["vendor-chunks/@ungap"];
exports.modules = {

/***/ "(ssr)/./node_modules/@ungap/structured-clone/esm/deserialize.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@ungap/structured-clone/esm/deserialize.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deserialize: () => (/* binding */ deserialize)\n/* harmony export */ });\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/@ungap/structured-clone/esm/types.js\");\n\n\nconst env = typeof self === 'object' ? self : globalThis;\n\nconst deserializer = ($, _) => {\n  const as = (out, index) => {\n    $.set(index, out);\n    return out;\n  };\n\n  const unpair = index => {\n    if ($.has(index))\n      return $.get(index);\n\n    const [type, value] = _[index];\n    switch (type) {\n      case _types_js__WEBPACK_IMPORTED_MODULE_0__.PRIMITIVE:\n      case _types_js__WEBPACK_IMPORTED_MODULE_0__.VOID:\n        return as(value, index);\n      case _types_js__WEBPACK_IMPORTED_MODULE_0__.ARRAY: {\n        const arr = as([], index);\n        for (const index of value)\n          arr.push(unpair(index));\n        return arr;\n      }\n      case _types_js__WEBPACK_IMPORTED_MODULE_0__.OBJECT: {\n        const object = as({}, index);\n        for (const [key, index] of value)\n          object[unpair(key)] = unpair(index);\n        return object;\n      }\n      case _types_js__WEBPACK_IMPORTED_MODULE_0__.DATE:\n        return as(new Date(value), index);\n      case _types_js__WEBPACK_IMPORTED_MODULE_0__.REGEXP: {\n        const {source, flags} = value;\n        return as(new RegExp(source, flags), index);\n      }\n      case _types_js__WEBPACK_IMPORTED_MODULE_0__.MAP: {\n        const map = as(new Map, index);\n        for (const [key, index] of value)\n          map.set(unpair(key), unpair(index));\n        return map;\n      }\n      case _types_js__WEBPACK_IMPORTED_MODULE_0__.SET: {\n        const set = as(new Set, index);\n        for (const index of value)\n          set.add(unpair(index));\n        return set;\n      }\n      case _types_js__WEBPACK_IMPORTED_MODULE_0__.ERROR: {\n        const {name, message} = value;\n        return as(new env[name](message), index);\n      }\n      case _types_js__WEBPACK_IMPORTED_MODULE_0__.BIGINT:\n        return as(BigInt(value), index);\n      case 'BigInt':\n        return as(Object(BigInt(value)), index);\n      case 'ArrayBuffer':\n        return as(new Uint8Array(value).buffer, value);\n      case 'DataView': {\n        const { buffer } = new Uint8Array(value);\n        return as(new DataView(buffer), value);\n      }\n    }\n    return as(new env[type](value), index);\n  };\n\n  return unpair;\n};\n\n/**\n * @typedef {Array<string,any>} Record a type representation\n */\n\n/**\n * Returns a deserialized value from a serialized array of Records.\n * @param {Record[]} serialized a previously serialized value.\n * @returns {any}\n */\nconst deserialize = serialized => deserializer(new Map, serialized)(0);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ungap/structured-clone/esm/deserialize.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ungap/structured-clone/esm/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/@ungap/structured-clone/esm/index.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   deserialize: () => (/* reexport safe */ _deserialize_js__WEBPACK_IMPORTED_MODULE_0__.deserialize),\n/* harmony export */   serialize: () => (/* reexport safe */ _serialize_js__WEBPACK_IMPORTED_MODULE_1__.serialize)\n/* harmony export */ });\n/* harmony import */ var _deserialize_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./deserialize.js */ \"(ssr)/./node_modules/@ungap/structured-clone/esm/deserialize.js\");\n/* harmony import */ var _serialize_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./serialize.js */ \"(ssr)/./node_modules/@ungap/structured-clone/esm/serialize.js\");\n\n\n\n/**\n * @typedef {Array<string,any>} Record a type representation\n */\n\n/**\n * Returns an array of serialized Records.\n * @param {any} any a serializable value.\n * @param {{transfer?: any[], json?: boolean, lossy?: boolean}?} options an object with\n * a transfer option (ignored when polyfilled) and/or non standard fields that\n * fallback to the polyfill if present.\n * @returns {Record[]}\n */\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (typeof structuredClone === \"function\" ?\n  /* c8 ignore start */\n  (any, options) => (\n    options && ('json' in options || 'lossy' in options) ?\n      (0,_deserialize_js__WEBPACK_IMPORTED_MODULE_0__.deserialize)((0,_serialize_js__WEBPACK_IMPORTED_MODULE_1__.serialize)(any, options)) : structuredClone(any)\n  ) :\n  (any, options) => (0,_deserialize_js__WEBPACK_IMPORTED_MODULE_0__.deserialize)((0,_serialize_js__WEBPACK_IMPORTED_MODULE_1__.serialize)(any, options)));\n  /* c8 ignore stop */\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVuZ2FwL3N0cnVjdHVyZWQtY2xvbmUvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQTZDO0FBQ0o7O0FBRXpDO0FBQ0EsYUFBYSxtQkFBbUI7QUFDaEM7O0FBRUE7QUFDQTtBQUNBLFdBQVcsS0FBSztBQUNoQixZQUFZLGtEQUFrRCxHQUFHO0FBQ2pFO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQSxpRUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBLE1BQU0sNERBQVcsQ0FBQyx3REFBUztBQUMzQjtBQUNBLG9CQUFvQiw0REFBVyxDQUFDLHdEQUFTLGVBQWUsRUFBQztBQUN6RDs7QUFFZ0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yYWZ0aG9yLy4vbm9kZV9tb2R1bGVzL0B1bmdhcC9zdHJ1Y3R1cmVkLWNsb25lL2VzbS9pbmRleC5qcz8yYmFjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7ZGVzZXJpYWxpemV9IGZyb20gJy4vZGVzZXJpYWxpemUuanMnO1xuaW1wb3J0IHtzZXJpYWxpemV9IGZyb20gJy4vc2VyaWFsaXplLmpzJztcblxuLyoqXG4gKiBAdHlwZWRlZiB7QXJyYXk8c3RyaW5nLGFueT59IFJlY29yZCBhIHR5cGUgcmVwcmVzZW50YXRpb25cbiAqL1xuXG4vKipcbiAqIFJldHVybnMgYW4gYXJyYXkgb2Ygc2VyaWFsaXplZCBSZWNvcmRzLlxuICogQHBhcmFtIHthbnl9IGFueSBhIHNlcmlhbGl6YWJsZSB2YWx1ZS5cbiAqIEBwYXJhbSB7e3RyYW5zZmVyPzogYW55W10sIGpzb24/OiBib29sZWFuLCBsb3NzeT86IGJvb2xlYW59P30gb3B0aW9ucyBhbiBvYmplY3Qgd2l0aFxuICogYSB0cmFuc2ZlciBvcHRpb24gKGlnbm9yZWQgd2hlbiBwb2x5ZmlsbGVkKSBhbmQvb3Igbm9uIHN0YW5kYXJkIGZpZWxkcyB0aGF0XG4gKiBmYWxsYmFjayB0byB0aGUgcG9seWZpbGwgaWYgcHJlc2VudC5cbiAqIEByZXR1cm5zIHtSZWNvcmRbXX1cbiAqL1xuZXhwb3J0IGRlZmF1bHQgdHlwZW9mIHN0cnVjdHVyZWRDbG9uZSA9PT0gXCJmdW5jdGlvblwiID9cbiAgLyogYzggaWdub3JlIHN0YXJ0ICovXG4gIChhbnksIG9wdGlvbnMpID0+IChcbiAgICBvcHRpb25zICYmICgnanNvbicgaW4gb3B0aW9ucyB8fCAnbG9zc3knIGluIG9wdGlvbnMpID9cbiAgICAgIGRlc2VyaWFsaXplKHNlcmlhbGl6ZShhbnksIG9wdGlvbnMpKSA6IHN0cnVjdHVyZWRDbG9uZShhbnkpXG4gICkgOlxuICAoYW55LCBvcHRpb25zKSA9PiBkZXNlcmlhbGl6ZShzZXJpYWxpemUoYW55LCBvcHRpb25zKSk7XG4gIC8qIGM4IGlnbm9yZSBzdG9wICovXG5cbmV4cG9ydCB7ZGVzZXJpYWxpemUsIHNlcmlhbGl6ZX07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ungap/structured-clone/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ungap/structured-clone/esm/serialize.js":
/*!***************************************************************!*\
  !*** ./node_modules/@ungap/structured-clone/esm/serialize.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   serialize: () => (/* binding */ serialize)\n/* harmony export */ });\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/@ungap/structured-clone/esm/types.js\");\n\n\nconst EMPTY = '';\n\nconst {toString} = {};\nconst {keys} = Object;\n\nconst typeOf = value => {\n  const type = typeof value;\n  if (type !== 'object' || !value)\n    return [_types_js__WEBPACK_IMPORTED_MODULE_0__.PRIMITIVE, type];\n\n  const asString = toString.call(value).slice(8, -1);\n  switch (asString) {\n    case 'Array':\n      return [_types_js__WEBPACK_IMPORTED_MODULE_0__.ARRAY, EMPTY];\n    case 'Object':\n      return [_types_js__WEBPACK_IMPORTED_MODULE_0__.OBJECT, EMPTY];\n    case 'Date':\n      return [_types_js__WEBPACK_IMPORTED_MODULE_0__.DATE, EMPTY];\n    case 'RegExp':\n      return [_types_js__WEBPACK_IMPORTED_MODULE_0__.REGEXP, EMPTY];\n    case 'Map':\n      return [_types_js__WEBPACK_IMPORTED_MODULE_0__.MAP, EMPTY];\n    case 'Set':\n      return [_types_js__WEBPACK_IMPORTED_MODULE_0__.SET, EMPTY];\n    case 'DataView':\n      return [_types_js__WEBPACK_IMPORTED_MODULE_0__.ARRAY, asString];\n  }\n\n  if (asString.includes('Array'))\n    return [_types_js__WEBPACK_IMPORTED_MODULE_0__.ARRAY, asString];\n\n  if (asString.includes('Error'))\n    return [_types_js__WEBPACK_IMPORTED_MODULE_0__.ERROR, asString];\n\n  return [_types_js__WEBPACK_IMPORTED_MODULE_0__.OBJECT, asString];\n};\n\nconst shouldSkip = ([TYPE, type]) => (\n  TYPE === _types_js__WEBPACK_IMPORTED_MODULE_0__.PRIMITIVE &&\n  (type === 'function' || type === 'symbol')\n);\n\nconst serializer = (strict, json, $, _) => {\n\n  const as = (out, value) => {\n    const index = _.push(out) - 1;\n    $.set(value, index);\n    return index;\n  };\n\n  const pair = value => {\n    if ($.has(value))\n      return $.get(value);\n\n    let [TYPE, type] = typeOf(value);\n    switch (TYPE) {\n      case _types_js__WEBPACK_IMPORTED_MODULE_0__.PRIMITIVE: {\n        let entry = value;\n        switch (type) {\n          case 'bigint':\n            TYPE = _types_js__WEBPACK_IMPORTED_MODULE_0__.BIGINT;\n            entry = value.toString();\n            break;\n          case 'function':\n          case 'symbol':\n            if (strict)\n              throw new TypeError('unable to serialize ' + type);\n            entry = null;\n            break;\n          case 'undefined':\n            return as([_types_js__WEBPACK_IMPORTED_MODULE_0__.VOID], value);\n        }\n        return as([TYPE, entry], value);\n      }\n      case _types_js__WEBPACK_IMPORTED_MODULE_0__.ARRAY: {\n        if (type) {\n          let spread = value;\n          if (type === 'DataView') {\n            spread = new Uint8Array(value.buffer);\n          }\n          else if (type === 'ArrayBuffer') {\n            spread = new Uint8Array(value);\n          }\n          return as([type, [...spread]], value);\n        }\n\n        const arr = [];\n        const index = as([TYPE, arr], value);\n        for (const entry of value)\n          arr.push(pair(entry));\n        return index;\n      }\n      case _types_js__WEBPACK_IMPORTED_MODULE_0__.OBJECT: {\n        if (type) {\n          switch (type) {\n            case 'BigInt':\n              return as([type, value.toString()], value);\n            case 'Boolean':\n            case 'Number':\n            case 'String':\n              return as([type, value.valueOf()], value);\n          }\n        }\n\n        if (json && ('toJSON' in value))\n          return pair(value.toJSON());\n\n        const entries = [];\n        const index = as([TYPE, entries], value);\n        for (const key of keys(value)) {\n          if (strict || !shouldSkip(typeOf(value[key])))\n            entries.push([pair(key), pair(value[key])]);\n        }\n        return index;\n      }\n      case _types_js__WEBPACK_IMPORTED_MODULE_0__.DATE:\n        return as([TYPE, value.toISOString()], value);\n      case _types_js__WEBPACK_IMPORTED_MODULE_0__.REGEXP: {\n        const {source, flags} = value;\n        return as([TYPE, {source, flags}], value);\n      }\n      case _types_js__WEBPACK_IMPORTED_MODULE_0__.MAP: {\n        const entries = [];\n        const index = as([TYPE, entries], value);\n        for (const [key, entry] of value) {\n          if (strict || !(shouldSkip(typeOf(key)) || shouldSkip(typeOf(entry))))\n            entries.push([pair(key), pair(entry)]);\n        }\n        return index;\n      }\n      case _types_js__WEBPACK_IMPORTED_MODULE_0__.SET: {\n        const entries = [];\n        const index = as([TYPE, entries], value);\n        for (const entry of value) {\n          if (strict || !shouldSkip(typeOf(entry)))\n            entries.push(pair(entry));\n        }\n        return index;\n      }\n    }\n\n    const {message} = value;\n    return as([TYPE, {name: type, message}], value);\n  };\n\n  return pair;\n};\n\n/**\n * @typedef {Array<string,any>} Record a type representation\n */\n\n/**\n * Returns an array of serialized Records.\n * @param {any} value a serializable value.\n * @param {{json?: boolean, lossy?: boolean}?} options an object with a `lossy` or `json` property that,\n *  if `true`, will not throw errors on incompatible types, and behave more\n *  like JSON stringify would behave. Symbol and Function will be discarded.\n * @returns {Record[]}\n */\n const serialize = (value, {json, lossy} = {}) => {\n  const _ = [];\n  return serializer(!(json || lossy), !!json, new Map, _)(value), _;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ungap/structured-clone/esm/serialize.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ungap/structured-clone/esm/types.js":
/*!***********************************************************!*\
  !*** ./node_modules/@ungap/structured-clone/esm/types.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ARRAY: () => (/* binding */ ARRAY),\n/* harmony export */   BIGINT: () => (/* binding */ BIGINT),\n/* harmony export */   DATE: () => (/* binding */ DATE),\n/* harmony export */   ERROR: () => (/* binding */ ERROR),\n/* harmony export */   MAP: () => (/* binding */ MAP),\n/* harmony export */   OBJECT: () => (/* binding */ OBJECT),\n/* harmony export */   PRIMITIVE: () => (/* binding */ PRIMITIVE),\n/* harmony export */   REGEXP: () => (/* binding */ REGEXP),\n/* harmony export */   SET: () => (/* binding */ SET),\n/* harmony export */   VOID: () => (/* binding */ VOID)\n/* harmony export */ });\nconst VOID       = -1;\nconst PRIMITIVE  = 0;\nconst ARRAY      = 1;\nconst OBJECT     = 2;\nconst DATE       = 3;\nconst REGEXP     = 4;\nconst MAP        = 5;\nconst SET        = 6;\nconst ERROR      = 7;\nconst BIGINT     = 8;\n// export const SYMBOL = 9;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVuZ2FwL3N0cnVjdHVyZWQtY2xvbmUvZXNtL3R5cGVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBTztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcmFmdGhvci8uL25vZGVfbW9kdWxlcy9AdW5nYXAvc3RydWN0dXJlZC1jbG9uZS9lc20vdHlwZXMuanM/MjQ5YSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgVk9JRCAgICAgICA9IC0xO1xuZXhwb3J0IGNvbnN0IFBSSU1JVElWRSAgPSAwO1xuZXhwb3J0IGNvbnN0IEFSUkFZICAgICAgPSAxO1xuZXhwb3J0IGNvbnN0IE9CSkVDVCAgICAgPSAyO1xuZXhwb3J0IGNvbnN0IERBVEUgICAgICAgPSAzO1xuZXhwb3J0IGNvbnN0IFJFR0VYUCAgICAgPSA0O1xuZXhwb3J0IGNvbnN0IE1BUCAgICAgICAgPSA1O1xuZXhwb3J0IGNvbnN0IFNFVCAgICAgICAgPSA2O1xuZXhwb3J0IGNvbnN0IEVSUk9SICAgICAgPSA3O1xuZXhwb3J0IGNvbnN0IEJJR0lOVCAgICAgPSA4O1xuLy8gZXhwb3J0IGNvbnN0IFNZTUJPTCA9IDk7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ungap/structured-clone/esm/types.js\n");

/***/ })

};
;