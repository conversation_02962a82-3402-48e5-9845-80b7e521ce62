import { ChatMessage, ChatData } from '@/lib/types/chat';
import { 
  MessageStatistics, 
  DetailedStatistics, 
  WordFrequency, 
  DailyStats,
  WeeklyStats,
  MonthlyStats,
  ModelStats,
  AttachmentStats,
  TimeStats,
  FavoriteStats,
  StatisticsFilters 
} from '@/lib/types/statistics';

class StatisticsService {
  /**
   * Calcula estatísticas básicas de mensagens
   */
  calculateMessageStatistics(messages: ChatMessage[], filters?: StatisticsFilters): MessageStatistics {
    const filteredMessages = this.filterMessages(messages, filters);
    
    if (filteredMessages.length === 0) {
      return this.getEmptyStatistics();
    }

    const userMessages = filteredMessages.filter(m => m.role === 'user');
    const aiMessages = filteredMessages.filter(m => m.role === 'assistant');

    // Contagem de palavras
    const totalWordsUser = this.countWords(userMessages.map(m => m.content).join(' '));
    const totalWordsAI = this.countWords(aiMessages.map(m => m.content).join(' '));
    const totalWords = totalWordsUser + totalWordsAI;

    // Contagem de caracteres
    const totalCharactersUser = userMessages.reduce((sum, m) => sum + m.content.length, 0);
    const totalCharactersAI = aiMessages.reduce((sum, m) => sum + m.content.length, 0);
    const totalCharacters = totalCharactersUser + totalCharactersAI;

    // Tokens e custos
    const totalPromptTokens = filteredMessages.reduce((sum, m) => sum + (m.usage?.prompt_tokens || 0), 0);
    const totalCompletionTokens = filteredMessages.reduce((sum, m) => sum + (m.usage?.completion_tokens || 0), 0);
    const totalCost = filteredMessages.reduce((sum, m) => sum + (m.usage?.cost || 0), 0);

    // Tempo de resposta médio
    const responseTimes = aiMessages.filter(m => m.responseTime).map(m => m.responseTime!);
    const averageResponseTime = responseTimes.length > 0 
      ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length 
      : 0;

    // Comprimento médio de mensagem
    const averageMessageLength = totalCharacters / filteredMessages.length;

    // Palavras por mensagem
    const averageWordsPerMessage = totalWords / filteredMessages.length;

    // Frases por mensagem (estimativa baseada em pontos finais)
    const totalSentences = filteredMessages.reduce((sum, m) => {
      return sum + (m.content.match(/[.!?]+/g) || []).length;
    }, 0);
    const averageSentencesPerMessage = totalSentences / filteredMessages.length;

    // Tempo estimado de leitura (250 palavras por minuto)
    const estimatedReadingTime = Math.ceil(totalWords / 250);

    // Palavras mais usadas
    const mostUsedWords = this.getMostUsedWords(filteredMessages.map(m => m.content).join(' '));

    return {
      totalMessages: filteredMessages.length,
      totalWords,
      totalWordsAI,
      totalWordsUser,
      totalPromptTokens,
      totalCompletionTokens,
      totalCost,
      averageResponseTime,
      averageMessageLength,
      averageWordsPerMessage,
      averageSentencesPerMessage,
      estimatedReadingTime,
      totalCharactersAI,
      totalCharactersUser,
      totalCharacters,
      mostUsedWords
    };
  }

  /**
   * Calcula estatísticas detalhadas incluindo análises temporais e por modelo
   */
  calculateDetailedStatistics(
    messages: ChatMessage[], 
    chatData?: ChatData[], 
    filters?: StatisticsFilters
  ): DetailedStatistics {
    const basicStats = this.calculateMessageStatistics(messages, filters);
    const filteredMessages = this.filterMessages(messages, filters);

    return {
      ...basicStats,
      dailyStats: this.calculateDailyStats(filteredMessages),
      weeklyStats: this.calculateWeeklyStats(filteredMessages),
      monthlyStats: this.calculateMonthlyStats(filteredMessages),
      modelStats: this.calculateModelStats(filteredMessages),
      attachmentStats: this.calculateAttachmentStats(filteredMessages),
      timeStats: this.calculateTimeStats(filteredMessages, chatData),
      favoriteStats: this.calculateFavoriteStats(filteredMessages)
    };
  }

  /**
   * Filtra mensagens baseado nos filtros fornecidos
   */
  private filterMessages(messages: ChatMessage[], filters?: StatisticsFilters): ChatMessage[] {
    if (!filters) return messages;

    let filtered = [...messages];

    if (filters.dateRange) {
      filtered = filtered.filter(m => {
        const messageDate = new Date(m.timestamp);
        return messageDate >= filters.dateRange!.start && messageDate <= filters.dateRange!.end;
      });
    }

    return filtered;
  }

  /**
   * Conta palavras em um texto
   */
  private countWords(text: string): number {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * Obtém as palavras mais usadas
   */
  private getMostUsedWords(text: string, limit: number = 10): WordFrequency[] {
    const words = text.toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 2); // Filtrar palavras muito pequenas

    const wordCount = new Map<string, number>();
    words.forEach(word => {
      wordCount.set(word, (wordCount.get(word) || 0) + 1);
    });

    const totalWords = words.length;
    const sortedWords = Array.from(wordCount.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, limit)
      .map(([word, count]) => ({
        word,
        count,
        percentage: (count / totalWords) * 100
      }));

    return sortedWords;
  }

  /**
   * Calcula estatísticas diárias
   */
  private calculateDailyStats(messages: ChatMessage[]): DailyStats[] {
    const dailyMap = new Map<string, {
      messages: ChatMessage[];
      date: string;
    }>();

    messages.forEach(message => {
      const date = new Date(message.timestamp).toISOString().split('T')[0];
      if (!dailyMap.has(date)) {
        dailyMap.set(date, { messages: [], date });
      }
      dailyMap.get(date)!.messages.push(message);
    });

    return Array.from(dailyMap.values()).map(day => {
      const words = this.countWords(day.messages.map(m => m.content).join(' '));
      const tokens = day.messages.reduce((sum, m) => sum + (m.usage?.total_tokens || 0), 0);
      const cost = day.messages.reduce((sum, m) => sum + (m.usage?.cost || 0), 0);
      const responseTimes = day.messages.filter(m => m.responseTime).map(m => m.responseTime!);
      const averageResponseTime = responseTimes.length > 0 
        ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length 
        : 0;

      return {
        date: day.date,
        messages: day.messages.length,
        words,
        tokens,
        cost,
        averageResponseTime
      };
    }).sort((a, b) => a.date.localeCompare(b.date));
  }

  /**
   * Calcula estatísticas semanais
   */
  private calculateWeeklyStats(messages: ChatMessage[]): WeeklyStats[] {
    // Implementação similar às estatísticas diárias, mas agrupando por semana
    const weeklyMap = new Map<string, ChatMessage[]>();

    messages.forEach(message => {
      const date = new Date(message.timestamp);
      const year = date.getFullYear();
      const week = this.getWeekNumber(date);
      const weekKey = `${year}-W${week.toString().padStart(2, '0')}`;
      
      if (!weeklyMap.has(weekKey)) {
        weeklyMap.set(weekKey, []);
      }
      weeklyMap.get(weekKey)!.push(message);
    });

    return Array.from(weeklyMap.entries()).map(([week, weekMessages]) => {
      const words = this.countWords(weekMessages.map(m => m.content).join(' '));
      const tokens = weekMessages.reduce((sum, m) => sum + (m.usage?.total_tokens || 0), 0);
      const cost = weekMessages.reduce((sum, m) => sum + (m.usage?.cost || 0), 0);
      const responseTimes = weekMessages.filter(m => m.responseTime).map(m => m.responseTime!);
      const averageResponseTime = responseTimes.length > 0 
        ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length 
        : 0;

      return {
        week,
        messages: weekMessages.length,
        words,
        tokens,
        cost,
        averageResponseTime
      };
    }).sort((a, b) => a.week.localeCompare(b.week));
  }

  /**
   * Calcula estatísticas mensais
   */
  private calculateMonthlyStats(messages: ChatMessage[]): MonthlyStats[] {
    const monthlyMap = new Map<string, ChatMessage[]>();

    messages.forEach(message => {
      const date = new Date(message.timestamp);
      const monthKey = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;
      
      if (!monthlyMap.has(monthKey)) {
        monthlyMap.set(monthKey, []);
      }
      monthlyMap.get(monthKey)!.push(message);
    });

    return Array.from(monthlyMap.entries()).map(([month, monthMessages]) => {
      const words = this.countWords(monthMessages.map(m => m.content).join(' '));
      const tokens = monthMessages.reduce((sum, m) => sum + (m.usage?.total_tokens || 0), 0);
      const cost = monthMessages.reduce((sum, m) => sum + (m.usage?.cost || 0), 0);
      const responseTimes = monthMessages.filter(m => m.responseTime).map(m => m.responseTime!);
      const averageResponseTime = responseTimes.length > 0 
        ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length 
        : 0;

      return {
        month,
        messages: monthMessages.length,
        words,
        tokens,
        cost,
        averageResponseTime
      };
    }).sort((a, b) => a.month.localeCompare(b.month));
  }

  /**
   * Obtém o número da semana do ano
   */
  private getWeekNumber(date: Date): number {
    const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
    const dayNum = d.getUTCDay() || 7;
    d.setUTCDate(d.getUTCDate() + 4 - dayNum);
    const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
    return Math.ceil((((d.getTime() - yearStart.getTime()) / 86400000) + 1) / 7);
  }

  /**
   * Calcula estatísticas por modelo
   */
  private calculateModelStats(messages: ChatMessage[]): ModelStats[] {
    // Como não temos informação do modelo nas mensagens, retornamos array vazio
    // Isso pode ser implementado quando tivermos essa informação
    return [];
  }

  /**
   * Calcula estatísticas de anexos
   */
  private calculateAttachmentStats(messages: ChatMessage[]): AttachmentStats {
    const messagesWithAttachments = messages.filter(m => m.attachments && m.attachments.length > 0);
    const allAttachments = messagesWithAttachments.flatMap(m => m.attachments || []);

    const imageAttachments = allAttachments.filter(a => a.type === 'image').length;
    const pdfAttachments = allAttachments.filter(a => a.type === 'pdf').length;
    const totalSize = allAttachments.reduce((sum, a) => sum + a.size, 0);
    const averageSize = allAttachments.length > 0 ? totalSize / allAttachments.length : 0;

    const attachmentsByType: { [key: string]: number } = {};
    allAttachments.forEach(attachment => {
      attachmentsByType[attachment.type] = (attachmentsByType[attachment.type] || 0) + 1;
    });

    return {
      totalAttachments: allAttachments.length,
      imageAttachments,
      pdfAttachments,
      totalSize,
      averageSize,
      attachmentsByType
    };
  }

  /**
   * Calcula estatísticas de tempo
   */
  private calculateTimeStats(messages: ChatMessage[], chatData?: ChatData[]): TimeStats {
    const timeDistribution: { [hour: number]: number } = {};
    const dayDistribution: { [day: string]: number } = {};
    const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];

    // Inicializar distribuições
    for (let i = 0; i < 24; i++) {
      timeDistribution[i] = 0;
    }
    days.forEach(day => {
      dayDistribution[day] = 0;
    });

    messages.forEach(message => {
      const date = new Date(message.timestamp);
      const hour = date.getHours();
      const day = days[date.getDay()];

      timeDistribution[hour]++;
      dayDistribution[day]++;
    });

    // Encontrar hora mais ativa
    const mostActiveHour = Object.entries(timeDistribution)
      .reduce((max, [hour, count]) => count > max.count ? { hour: parseInt(hour), count } : max, { hour: 0, count: 0 })
      .hour;

    // Encontrar dia mais ativo
    const mostActiveDay = Object.entries(dayDistribution)
      .reduce((max, [day, count]) => count > max.count ? { day, count } : max, { day: 'monday', count: 0 })
      .day;

    // Calcular estatísticas de sessão se chatData estiver disponível
    let totalSessionTime = 0;
    let averageSessionTime = 0;
    let longestSession = 0;
    let shortestSession = 0;
    let sessionsCount = 0;

    if (chatData) {
      const sessionTimes = chatData
        .filter(chat => chat.sessionTime)
        .map(chat => chat.sessionTime!.totalTime);

      if (sessionTimes.length > 0) {
        totalSessionTime = sessionTimes.reduce((sum, time) => sum + time, 0);
        averageSessionTime = totalSessionTime / sessionTimes.length;
        longestSession = Math.max(...sessionTimes);
        shortestSession = Math.min(...sessionTimes);
        sessionsCount = sessionTimes.length;
      }
    }

    return {
      totalSessionTime,
      averageSessionTime,
      longestSession,
      shortestSession,
      sessionsCount,
      mostActiveHour,
      mostActiveDay,
      timeDistribution
    };
  }

  /**
   * Calcula estatísticas de favoritos
   */
  private calculateFavoriteStats(messages: ChatMessage[]): FavoriteStats {
    const favoriteMessages = messages.filter(m => m.isFavorite);

    const favoritesByRole = {
      user: favoriteMessages.filter(m => m.role === 'user').length,
      assistant: favoriteMessages.filter(m => m.role === 'assistant').length
    };

    const averageFavoriteLength = favoriteMessages.length > 0
      ? favoriteMessages.reduce((sum, m) => sum + m.content.length, 0) / favoriteMessages.length
      : 0;

    const mostFavoritedWords = favoriteMessages.length > 0
      ? this.getMostUsedWords(favoriteMessages.map(m => m.content).join(' '), 5)
      : [];

    return {
      totalFavorites: favoriteMessages.length,
      favoritesByRole,
      averageFavoriteLength,
      mostFavoritedWords
    };
  }

  /**
   * Retorna estatísticas vazias
   */
  private getEmptyStatistics(): MessageStatistics {
    return {
      totalMessages: 0,
      totalWords: 0,
      totalWordsAI: 0,
      totalWordsUser: 0,
      totalPromptTokens: 0,
      totalCompletionTokens: 0,
      totalCost: 0,
      averageResponseTime: 0,
      averageMessageLength: 0,
      averageWordsPerMessage: 0,
      averageSentencesPerMessage: 0,
      estimatedReadingTime: 0,
      totalCharactersAI: 0,
      totalCharactersUser: 0,
      totalCharacters: 0,
      mostUsedWords: []
    };
  }
}

export default new StatisticsService();
