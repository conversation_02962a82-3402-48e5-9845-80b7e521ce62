// Serviço para integração com Firebase Functions de IA

export interface ChatMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: string;
  isFavorite?: boolean;
  attachments?: import('@/lib/types/chat').AttachmentMetadata[];
}

export interface StreamResponse {
  content: string;
  isComplete: boolean;
  error?: string;
}

export interface AIServiceConfig {
  username: string;
  chatId: string;
  message: string;
  model?: string;
  attachments?: import('@/lib/types/chat').AttachmentMetadata[];
  isRegeneration?: boolean;
  webSearchEnabled?: boolean;
  userMessageId?: string;
}

class AIService {
  private readonly functionUrl = 'https://us-central1-rafthor-0001.cloudfunctions.net/chatWithAI';
  private abortController: AbortController | null = null;

  /**
   * Envia mensagem para a IA com streaming
   */
  async sendMessage(
    config: AIServiceConfig,
    onChunk: (chunk: string) => void,
    onComplete: (fullResponse: string) => void,
    onError: (error: string) => void
  ): Promise<void> {
    try {
      // Criar novo AbortController para esta requisição
      this.abortController = new AbortController();

      // Preparar dados da requisição
      const requestData = {
        username: config.username,
        chatId: config.chatId,
        message: config.message,
        model: config.model,
        attachments: config.attachments || [],
        isRegeneration: config.isRegeneration || false,
        webSearchEnabled: config.webSearchEnabled || false,
        userMessageId: config.userMessageId,
      };

      console.log('=== DEBUG: AI SERVICE REQUEST DATA ===');
      console.log('Request data:', JSON.stringify(requestData, null, 2));
      console.log('Attachments length:', requestData.attachments.length);
      if (requestData.attachments.length > 0) {
        console.log('First attachment:', JSON.stringify(requestData.attachments[0], null, 2));
      }

      // Enviar requisição





      const response = await fetch(this.functionUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
        signal: this.abortController.signal,
      });


      console.log('🌐 URL:', response.url);
      console.groupEnd();

      if (!response.ok) {
        const errorData = await response.json();
        console.group('❌ AI SERVICE - ERRO NA RESPOSTA');
        console.error('Status:', response.status);
        console.error('Status Text:', response.statusText);
        console.error('Error Data:', errorData);
        console.groupEnd();
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      if (!response.body) {
        throw new Error('Response body is not available');
      }

      // Processar stream
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let fullResponse = '';

      // Processar stream

      try {
        while (true) {
          const { done, value } = await reader.read();

          if (done) {
            break;
          }

          const chunk = decoder.decode(value, { stream: true });

          fullResponse += chunk;

          // Chamar callback para cada chunk
          onChunk(chunk);
        }

        // Chamar callback de conclusão
        onComplete(fullResponse);

      } finally {
        reader.releaseLock();
      }

    } catch (error) {
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          return;
        }
        onError(error.message);
      } else {
        onError('Erro desconhecido na comunicação com a IA');
      }
    } finally {
      this.abortController = null;
    }
  }

  /**
   * Cancela a requisição em andamento
   */
  cancelRequest(): void {
    if (this.abortController) {
      this.abortController.abort();
      this.abortController = null;
    }
  }

  /**
   * Verifica se há uma requisição em andamento
   */
  isRequestInProgress(): boolean {
    return this.abortController !== null;
  }

  /**
   * Carrega mensagens de um chat usando a API route
   */
  async loadChatMessages(username: string, chatId: string): Promise<ChatMessage[]> {
    try {
      const response = await fetch(`/api/chat/${username}/${chatId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Erro ao carregar chat: ${response.statusText}`);
      }

      const chatData = await response.json();
      return chatData.messages || [];

    } catch (error) {
      console.error('Erro ao carregar mensagens do chat:', error);
      return [];
    }
  }

  /**
   * Converte mensagens do formato interno para o formato da IA
   */
  convertToAIFormat(messages: any[]): ChatMessage[] {
    return messages.map(msg => ({
      id: msg.id,
      content: msg.content,
      role: msg.sender === 'user' ? 'user' : 'assistant',
      timestamp: msg.timestamp,
      isFavorite: msg.isFavorite || false,
      attachments: msg.attachments || [], // ✅ INCLUIR ANEXOS!
    }));
  }

  /**
   * Converte mensagens do formato da IA para o formato interno
   */
  convertFromAIFormat(messages: ChatMessage[]): any[] {
    return messages.map(msg => ({
      id: msg.id,
      content: msg.content,
      sender: msg.role === 'user' ? 'user' : 'ai',
      timestamp: msg.timestamp,
      isFavorite: msg.isFavorite || false,
      attachments: msg.attachments || [], // ✅ INCLUIR ANEXOS!
    }));
  }

  /**
   * Gera ID único para mensagens
   */
  generateMessageId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  }

  /**
   * Valida configuração antes de enviar
   */
  private validateConfig(config: AIServiceConfig): void {
    if (!config.username?.trim()) {
      throw new Error('Username é obrigatório');
    }
    if (!config.chatId?.trim()) {
      throw new Error('Chat ID é obrigatório');
    }
    if (!config.message?.trim()) {
      throw new Error('Mensagem é obrigatória');
    }
    if (config.message.length > 10000) {
      throw new Error('Mensagem muito longa (máximo 10.000 caracteres)');
    }
  }

  /**
   * Envia mensagem com validação
   */
  async sendMessageSafe(
    config: AIServiceConfig,
    onChunk: (chunk: string) => void,
    onComplete: (fullResponse: string) => void,
    onError: (error: string) => void
  ): Promise<void> {
    try {
      this.validateConfig(config);
      await this.sendMessage(config, onChunk, onComplete, onError);
    } catch (error) {
      if (error instanceof Error) {
        onError(error.message);
      } else {
        onError('Erro de validação');
      }
    }
  }

  /**
   * Deleta uma mensagem do chat no Firebase Storage
   */
  async deleteMessage(username: string, chatId: string, messageId: string): Promise<boolean> {
    try {
      const response = await fetch(`/api/chat/${username}/${chatId}/message/${messageId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Erro ao deletar mensagem: ${response.statusText}`);
      }

      return true;
    } catch (error) {
      console.error('Erro ao deletar mensagem:', error);
      return false;
    }
  }

  /**
   * Atualiza uma mensagem no chat no Firebase Storage
   */
  async updateMessage(username: string, chatId: string, messageId: string, newContent: string): Promise<boolean> {
    try {
      const response = await fetch(`/api/chat/${username}/${chatId}/message/${messageId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ content: newContent }),
      });

      if (!response.ok) {
        throw new Error(`Erro ao atualizar mensagem: ${response.statusText}`);
      }

      return true;
    } catch (error) {
      console.error('Erro ao atualizar mensagem:', error);
      return false;
    }
  }
}

// Exportar instância singleton
export const aiService = new AIService();
export default aiService;
