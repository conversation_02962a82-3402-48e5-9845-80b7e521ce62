import { doc, getDoc, setDoc, collection, getDocs, updateDoc, deleteDoc } from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { db, storage } from '@/lib/firebase';

// Interfaces para configurações do usuário
export interface UserSettings {
  username: string;
  email: string;
  profilePhotoURL?: string;
  chatAppearance: {
    fontSize: number;
    fontFamily: string;
  };
  streaming: StreamingSettings;
  chatSessions: {
    enabled: boolean;
    wordsPerSession: number;
  };
}

export interface StreamingSettings {
  enabled: boolean;
}

export interface APIEndpoint {
  id: string;
  name: string;
  url: string;
  apiKey: string;
  isActive: boolean;
  createdAt: number;
  lastUsedAt?: number;
  settings?: {
    timeout?: number;
    retries?: number;
    [key: string]: any;
  };
}

// Serviços para configurações do usuário
export const updateUserSettings = async (userId: string, settings: Partial<UserSettings>): Promise<void> => {
  try {
    const userRef = doc(db, 'usuarios', userId);
    // Usar setDoc com merge para criar o documento se não existir ou atualizar se existir
    await setDoc(userRef, settings, { merge: true });
    console.log('User settings updated successfully');
  } catch (error) {
    console.error('Error updating user settings:', error);
    throw error;
  }
};

export const getUserSettings = async (userId: string): Promise<UserSettings | null> => {
  try {
    const userRef = doc(db, 'usuarios', userId);
    const userDoc = await getDoc(userRef);

    if (userDoc.exists()) {
      const data = userDoc.data();
      return {
        username: data.username || '',
        email: data.email || '',
        profilePhotoURL: data.profilePhotoURL,
        chatAppearance: data.chatAppearance || {
          fontSize: 14,
          fontFamily: 'Inter'
        },
        streaming: data.streaming || {
          enabled: false
        },
        chatSessions: data.chatSessions || {
          enabled: false,
          wordsPerSession: 5000
        }
      };
    }
    return null;
  } catch (error) {
    console.error('Error getting user settings:', error);
    throw error;
  }
};

// Serviços para foto de perfil
export const uploadProfilePhoto = async (userId: string, file: File): Promise<string> => {
  try {
    // Validar arquivo
    if (!file.type.startsWith('image/')) {
      throw new Error('Arquivo deve ser uma imagem');
    }
    
    if (file.size > 5 * 1024 * 1024) { // 5MB
      throw new Error('Arquivo deve ter menos de 5MB');
    }

    // Upload para Storage
    const photoRef = ref(storage, `usuarios/${userId}/configuracoes/foto-perfil`);
    await uploadBytes(photoRef, file);
    
    // Obter URL de download
    const downloadURL = await getDownloadURL(photoRef);
    
    // Atualizar Firestore
    await updateUserSettings(userId, { profilePhotoURL: downloadURL });
    
    return downloadURL;
  } catch (error) {
    console.error('Error uploading profile photo:', error);
    throw error;
  }
};

// Serviços para API Endpoints
export const createAPIEndpoint = async (userId: string, endpoint: Omit<APIEndpoint, 'id' | 'createdAt'>): Promise<string> => {
  try {
    const endpointId = `endpoint_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const endpointData: APIEndpoint = {
      ...endpoint,
      id: endpointId,
      createdAt: Date.now()
    };

    const endpointRef = doc(db, 'usuarios', userId, 'endpoints', endpointId);
    await setDoc(endpointRef, endpointData);

    console.log('API endpoint created successfully');
    return endpointId;
  } catch (error) {
    console.error('Error creating API endpoint:', error);
    throw error;
  }
};

export const getUserAPIEndpoints = async (userId: string): Promise<APIEndpoint[]> => {
  try {
    // Primeiro, tentar buscar na nova estrutura
    const endpointsRef = collection(db, 'usuarios', userId, 'endpoints');
    const endpointsSnapshot = await getDocs(endpointsRef);

    if (!endpointsSnapshot.empty) {
      const endpoints: APIEndpoint[] = [];
      endpointsSnapshot.forEach(doc => {
        endpoints.push(doc.data() as APIEndpoint);
      });

      // Ordenar por data de criação (mais recente primeiro)
      endpoints.sort((a, b) => b.createdAt - a.createdAt);
      return endpoints;
    }

    // Se não encontrou na nova estrutura, buscar na estrutura antiga
    const configRef = doc(db, 'usuarios', userId, 'configuracoes', 'settings');
    const configDoc = await getDoc(configRef);

    if (configDoc.exists()) {
      const config = configDoc.data();
      if (config.endpoints) {
        const endpoints: APIEndpoint[] = [];

        // Converter da estrutura antiga para a nova
        Object.entries(config.endpoints).forEach(([key, endpoint]: [string, any]) => {
          endpoints.push({
            id: `legacy_${key}`,
            name: endpoint.nome || key,
            url: endpoint.url || '',
            apiKey: endpoint.apiKey || '',
            isActive: endpoint.ativo || false,
            createdAt: Date.now(),
            settings: {
              defaultModel: endpoint.modeloPadrao
            }
          });
        });

        return endpoints;
      }
    }

    // Se não encontrou nada, retornar endpoints padrão
    return [
      {
        id: 'default_openrouter',
        name: 'OpenRouter',
        url: 'https://openrouter.ai/api/v1/chat/completions',
        apiKey: '',
        isActive: false,
        createdAt: Date.now(),
        settings: {
          defaultModel: 'meta-llama/llama-3.1-8b-instruct:free'
        }
      },
      {
        id: 'default_deepseek',
        name: 'DeepSeek',
        url: 'https://api.deepseek.com/v1/chat/completions',
        apiKey: '',
        isActive: false,
        createdAt: Date.now(),
        settings: {
          defaultModel: 'deepseek-chat'
        }
      }
    ];
  } catch (error) {
    console.error('Error getting user API endpoints:', error);
    throw error;
  }
};

export const updateAPIEndpoint = async (userId: string, endpointId: string, updates: Partial<APIEndpoint>): Promise<void> => {
  try {
    const endpointRef = doc(db, 'usuarios', userId, 'endpoints', endpointId);
    await updateDoc(endpointRef, updates);
    console.log('API endpoint updated successfully');
  } catch (error) {
    console.error('Error updating API endpoint:', error);
    throw error;
  }
};

export const deleteAPIEndpoint = async (userId: string, endpointId: string): Promise<void> => {
  try {
    const endpointRef = doc(db, 'usuarios', userId, 'endpoints', endpointId);
    await deleteDoc(endpointRef);
    console.log('API endpoint deleted successfully');
  } catch (error) {
    console.error('Error deleting API endpoint:', error);
    throw error;
  }
};

export const getActiveAPIEndpoint = async (userId: string): Promise<APIEndpoint | null> => {
  try {
    const endpoints = await getUserAPIEndpoints(userId);
    return endpoints.find(endpoint => endpoint.isActive) || null;
  } catch (error) {
    console.error('Error getting active API endpoint:', error);
    throw error;
  }
};

export const setActiveAPIEndpoint = async (userId: string, endpointId: string, isActive: boolean): Promise<void> => {
  try {
    // Apenas ativar/desativar o endpoint específico
    await updateAPIEndpoint(userId, endpointId, { isActive });

    console.log(`API endpoint ${isActive ? 'activated' : 'deactivated'} successfully`);
  } catch (error) {
    console.error('Error setting active API endpoint:', error);
    throw error;
  }
};

// Serviços para configurações de streaming
export const updateStreamingSettings = async (userId: string, streaming: StreamingSettings): Promise<void> => {
  try {
    const userRef = doc(db, 'usuarios', userId);
    await setDoc(userRef, { streaming }, { merge: true });
    console.log('Streaming settings updated successfully');
  } catch (error) {
    console.error('Error updating streaming settings:', error);
    throw error;
  }
};

export const getStreamingSettings = async (userId: string): Promise<StreamingSettings> => {
  try {
    const userSettings = await getUserSettings(userId);
    return userSettings?.streaming || { enabled: false };
  } catch (error) {
    console.error('Error getting streaming settings:', error);
    return { enabled: false };
  }
};
