'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FileText, Image, Eye, Download, Check, X } from 'lucide-react';
import { AttachmentMetadata } from '@/lib/types/chat';

interface AttachmentsModalProps {
  isOpen: boolean;
  onClose: () => void;
  attachments: AttachmentMetadata[];
  activeAttachments: string[]; // IDs dos anexos ativos
  onToggleAttachment: (attachmentId: string) => void;
}

export default function AttachmentsModal({
  isOpen,
  onClose,
  attachments,
  activeAttachments,
  onToggleAttachment
}: AttachmentsModalProps) {
  const [expandedImage, setExpandedImage] = useState<string | null>(null);

  // Fechar modal com ESC
  useEffect(() => {
    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEsc);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEsc);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (timestamp: number): string => {
    return new Date(timestamp).toLocaleString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleImageClick = (url: string) => {
    setExpandedImage(url);
  };

  const handleDownload = (attachment: AttachmentMetadata) => {
    const link = document.createElement('a');
    link.href = attachment.url;
    link.download = attachment.filename;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const isAttachmentActive = (attachmentId: string): boolean => {
    return activeAttachments.includes(attachmentId);
  };

  const activeCount = attachments.filter(att => isAttachmentActive(att.id)).length;
  const totalCount = attachments.length;

  if (!isOpen) return null;

  return (
    <>
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={onClose}
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              className="bg-gradient-to-br from-blue-900/95 to-blue-800/95 backdrop-blur-sm
                        border border-blue-600/30 rounded-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden
                        shadow-2xl"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Header */}
              <div className="bg-gradient-to-r from-blue-600/20 to-cyan-600/20 border-b border-blue-600/30 p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-blue-500/20 rounded-full flex items-center justify-center border border-blue-500/30">
                      <svg className="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                      </svg>
                    </div>
                    <div>
                      <h2 className="text-xl font-semibold text-white">
                        Gerenciar Anexos
                      </h2>
                      <p className="text-blue-200 text-sm">
                        {activeCount} de {totalCount} anexos ativos no contexto
                      </p>
                    </div>
                  </div>
                  
                  <button
                    onClick={onClose}
                    className="p-2 rounded-lg bg-blue-800/40 hover:bg-blue-700/50 text-blue-300 hover:text-blue-200 transition-all duration-200 border border-blue-600/20"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>
              </div>

              {/* Content */}
              <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
                {attachments.length === 0 ? (
                  <div className="text-center py-12">
                    <div className="w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-4 border border-blue-500/30">
                      <svg className="w-8 h-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-medium text-white mb-2">
                      Nenhum anexo encontrado
                    </h3>
                    <p className="text-blue-200">
                      Este chat ainda não possui anexos enviados.
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {/* Controles globais */}
                    <div className="bg-blue-800/30 backdrop-blur-sm border border-blue-600/30 rounded-xl p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-white font-medium">Controles Rápidos</h3>
                          <p className="text-blue-200 text-sm">Ativar ou desativar todos os anexos</p>
                        </div>
                        <div className="flex space-x-2">
                          <button
                            onClick={() => attachments.forEach(att => {
                              if (!isAttachmentActive(att.id)) {
                                onToggleAttachment(att.id);
                              }
                            })}
                            className="px-4 py-2 bg-green-600/20 hover:bg-green-600/30 text-green-300 hover:text-green-200 rounded-lg border border-green-600/30 transition-all duration-200 text-sm"
                          >
                            Ativar Todos
                          </button>
                          <button
                            onClick={() => attachments.forEach(att => {
                              if (isAttachmentActive(att.id)) {
                                onToggleAttachment(att.id);
                              }
                            })}
                            className="px-4 py-2 bg-red-600/20 hover:bg-red-600/30 text-red-300 hover:text-red-200 rounded-lg border border-red-600/30 transition-all duration-200 text-sm"
                          >
                            Desativar Todos
                          </button>
                        </div>
                      </div>
                    </div>

                    {/* Lista de anexos */}
                    <div className="grid gap-4">
                      {attachments.map((attachment) => {
                        const isActive = isAttachmentActive(attachment.id);
                        
                        return (
                          <motion.div
                            key={attachment.id}
                            layout
                            className={`
                              bg-blue-800/30 backdrop-blur-sm border rounded-xl p-4 transition-all duration-200
                              ${isActive 
                                ? 'border-green-500/50 shadow-lg shadow-green-500/10' 
                                : 'border-blue-600/30 opacity-60'
                              }
                            `}
                          >
                            <div className="flex items-start space-x-4">
                              {/* Toggle Switch */}
                              <div className="flex-shrink-0 pt-1">
                                <button
                                  onClick={() => onToggleAttachment(attachment.id)}
                                  className={`
                                    relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200
                                    ${isActive ? 'bg-green-600' : 'bg-gray-600'}
                                  `}
                                >
                                  <span
                                    className={`
                                      inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200
                                      ${isActive ? 'translate-x-6' : 'translate-x-1'}
                                    `}
                                  />
                                </button>
                              </div>

                              {/* Attachment Info */}
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center space-x-2 mb-2">
                                  {attachment.type === 'image' ? (
                                    <Image className="w-5 h-5 text-blue-400 flex-shrink-0" />
                                  ) : (
                                    <FileText className="w-5 h-5 text-red-400 flex-shrink-0" />
                                  )}
                                  <h4 className="text-white font-medium truncate">
                                    {attachment.filename}
                                  </h4>
                                  {isActive && (
                                    <div className="flex items-center space-x-1 bg-green-600/20 px-2 py-1 rounded-full border border-green-600/30">
                                      <Check className="w-3 h-3 text-green-400" />
                                      <span className="text-green-300 text-xs font-medium">Ativo</span>
                                    </div>
                                  )}
                                </div>

                                <div className="flex items-center space-x-4 text-sm text-blue-200 mb-3">
                                  <span>{formatFileSize(attachment.size)}</span>
                                  <span>•</span>
                                  <span>{formatDate(attachment.uploadedAt)}</span>
                                  <span>•</span>
                                  <span className="capitalize">{attachment.type}</span>
                                </div>

                                {/* Preview para imagens */}
                                {attachment.type === 'image' && (
                                  <div className="mb-3">
                                    <img
                                      src={attachment.url}
                                      alt={attachment.filename}
                                      className="max-w-32 h-auto rounded-lg cursor-pointer hover:opacity-90 transition-opacity border border-blue-600/30"
                                      onClick={() => handleImageClick(attachment.url)}
                                    />
                                  </div>
                                )}

                                {/* Actions */}
                                <div className="flex items-center space-x-2">
                                  {attachment.type === 'image' && (
                                    <button
                                      onClick={() => handleImageClick(attachment.url)}
                                      className="flex items-center space-x-1 px-3 py-1.5 bg-blue-600/20 hover:bg-blue-600/30 text-blue-300 hover:text-blue-200 rounded-lg border border-blue-600/30 transition-all duration-200 text-sm"
                                    >
                                      <Eye className="w-4 h-4" />
                                      <span>Visualizar</span>
                                    </button>
                                  )}
                                  <button
                                    onClick={() => handleDownload(attachment)}
                                    className="flex items-center space-x-1 px-3 py-1.5 bg-gray-600/20 hover:bg-gray-600/30 text-gray-300 hover:text-gray-200 rounded-lg border border-gray-600/30 transition-all duration-200 text-sm"
                                  >
                                    <Download className="w-4 h-4" />
                                    <span>Download</span>
                                  </button>
                                </div>
                              </div>
                            </div>
                          </motion.div>
                        );
                      })}
                    </div>
                  </div>
                )}
              </div>

              {/* Footer */}
              <div className="bg-blue-800/30 backdrop-blur-sm border-t border-blue-600/30 p-4">
                <div className="flex items-center justify-between">
                  <div className="text-sm text-blue-200">
                    <p>
                      <strong>Dica:</strong> Anexos desativados não serão incluídos no contexto da conversa,
                      mas permanecerão salvos no chat.
                    </p>
                  </div>
                  <button
                    onClick={onClose}
                    className="px-6 py-2 bg-blue-600 hover:bg-blue-500 text-white rounded-lg transition-all duration-200 font-medium"
                  >
                    Fechar
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Modal de visualização de imagem expandida */}
      <AnimatePresence>
        {expandedImage && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/80 backdrop-blur-sm z-[60] flex items-center justify-center p-4"
            onClick={() => setExpandedImage(null)}
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              className="relative max-w-[90vw] max-h-[90vh]"
              onClick={(e) => e.stopPropagation()}
            >
              <img
                src={expandedImage}
                alt="Imagem expandida"
                className="max-w-full max-h-full object-contain rounded-lg"
              />
              <button
                onClick={() => setExpandedImage(null)}
                className="absolute top-4 right-4 p-2 bg-black/50 hover:bg-black/70 text-white rounded-full transition-all duration-200"
              >
                <X className="w-5 h-5" />
              </button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}
