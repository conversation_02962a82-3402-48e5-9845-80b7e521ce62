'use client';

import { useState } from 'react';
import { FileText, Image, Download, Eye } from 'lucide-react';
import { AttachmentMetadata } from '@/lib/types/chat';

interface AttachmentDisplayProps {
  attachments: AttachmentMetadata[];
  isUserMessage?: boolean;
}

export default function AttachmentDisplay({ attachments, isUserMessage = false }: AttachmentDisplayProps) {
  const [expandedImage, setExpandedImage] = useState<string | null>(null);

  if (!attachments || attachments.length === 0) {
    return null;
  }

  const handleImageClick = (url: string) => {
    setExpandedImage(url);
  };

  const handleDownload = async (attachment: AttachmentMetadata) => {
    try {
      const response = await fetch(attachment.url);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = attachment.filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Erro ao baixar arquivo:', error);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="mt-2 space-y-2">
      {attachments.map((attachment) => (
        <div
          key={attachment.id}
          className={`
            border rounded-lg p-3 max-w-sm
            ${isUserMessage 
              ? 'bg-blue-50 border-blue-200' 
              : 'bg-gray-50 border-gray-200'
            }
          `}
        >
          {attachment.type === 'image' ? (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Image className="w-4 h-4 text-blue-600" />
                <span className="text-sm font-medium text-gray-700 truncate">
                  {attachment.filename}
                </span>
                <span className="text-xs text-gray-500">
                  {formatFileSize(attachment.size)}
                </span>
              </div>
              
              <div className="relative">
                <img
                  src={attachment.url}
                  alt={attachment.filename}
                  className="max-w-full h-auto rounded cursor-pointer hover:opacity-90 transition-opacity"
                  style={{ maxHeight: '200px' }}
                  onClick={() => handleImageClick(attachment.url)}
                />
                <button
                  onClick={() => handleImageClick(attachment.url)}
                  className="absolute top-2 right-2 bg-black bg-opacity-50 text-white p-1 rounded hover:bg-opacity-70 transition-all"
                  title="Expandir imagem"
                >
                  <Eye className="w-4 h-4" />
                </button>
              </div>
              
              <div className="flex gap-2">
                <button
                  onClick={() => handleDownload(attachment)}
                  className="flex items-center gap-1 text-xs text-blue-600 hover:text-blue-800 transition-colors"
                >
                  <Download className="w-3 h-3" />
                  Baixar
                </button>
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <FileText className="w-4 h-4 text-red-600" />
                <span className="text-sm font-medium text-gray-700 truncate">
                  {attachment.filename}
                </span>
                <span className="text-xs text-gray-500">
                  {formatFileSize(attachment.size)}
                </span>
              </div>
              
              <div className="flex gap-2">
                <button
                  onClick={() => handleDownload(attachment)}
                  className="flex items-center gap-1 text-xs text-blue-600 hover:text-blue-800 transition-colors"
                >
                  <Download className="w-3 h-3" />
                  Baixar PDF
                </button>
              </div>
            </div>
          )}
        </div>
      ))}

      {/* Modal para expandir imagem */}
      {expandedImage && (
        <div
          className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4"
          onClick={() => setExpandedImage(null)}
        >
          <div className="relative max-w-full max-h-full">
            <img
              src={expandedImage}
              alt="Imagem expandida"
              className="max-w-full max-h-full object-contain"
            />
            <button
              onClick={() => setExpandedImage(null)}
              className="absolute top-4 right-4 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-all"
            >
              ✕
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
