"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-from-html";
exports.ids = ["vendor-chunks/hast-util-from-html"];
exports.modules = {

/***/ "(ssr)/./node_modules/hast-util-from-html/lib/errors.js":
/*!********************************************************!*\
  !*** ./node_modules/hast-util-from-html/lib/errors.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   errors: () => (/* binding */ errors)\n/* harmony export */ });\n/**\n * @typedef ErrorInfo\n *   Info on a `parse5` error.\n * @property {string} reason\n *   Reason of error.\n * @property {string} description\n *   More info on error.\n * @property {false} [url]\n *   Turn off if this is not documented in the html5 spec (optional).\n */\n\nconst errors = {\n  /** @type {ErrorInfo} */\n  abandonedHeadElementChild: {\n    reason: 'Unexpected metadata element after head',\n    description:\n      'Unexpected element after head. Expected the element before `</head>`',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  abruptClosingOfEmptyComment: {\n    reason: 'Unexpected abruptly closed empty comment',\n    description: 'Unexpected `>` or `->`. Expected `-->` to close comments'\n  },\n  /** @type {ErrorInfo} */\n  abruptDoctypePublicIdentifier: {\n    reason: 'Unexpected abruptly closed public identifier',\n    description:\n      'Unexpected `>`. Expected a closing `\"` or `\\'` after the public identifier'\n  },\n  /** @type {ErrorInfo} */\n  abruptDoctypeSystemIdentifier: {\n    reason: 'Unexpected abruptly closed system identifier',\n    description:\n      'Unexpected `>`. Expected a closing `\"` or `\\'` after the identifier identifier'\n  },\n  /** @type {ErrorInfo} */\n  absenceOfDigitsInNumericCharacterReference: {\n    reason: 'Unexpected non-digit at start of numeric character reference',\n    description:\n      'Unexpected `%c`. Expected `[0-9]` for decimal references or `[0-9a-fA-F]` for hexadecimal references'\n  },\n  /** @type {ErrorInfo} */\n  cdataInHtmlContent: {\n    reason: 'Unexpected CDATA section in HTML',\n    description:\n      'Unexpected `<![CDATA[` in HTML. Remove it, use a comment, or encode special characters instead'\n  },\n  /** @type {ErrorInfo} */\n  characterReferenceOutsideUnicodeRange: {\n    reason: 'Unexpected too big numeric character reference',\n    description:\n      'Unexpectedly high character reference. Expected character references to be at most hexadecimal 10ffff (or decimal 1114111)'\n  },\n  /** @type {ErrorInfo} */\n  closingOfElementWithOpenChildElements: {\n    reason: 'Unexpected closing tag with open child elements',\n    description:\n      'Unexpectedly closing tag. Expected other tags to be closed first',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  controlCharacterInInputStream: {\n    reason: 'Unexpected control character',\n    description:\n      'Unexpected control character `%x`. Expected a non-control code point, 0x00, or ASCII whitespace'\n  },\n  /** @type {ErrorInfo} */\n  controlCharacterReference: {\n    reason: 'Unexpected control character reference',\n    description:\n      'Unexpectedly control character in reference. Expected a non-control code point, 0x00, or ASCII whitespace'\n  },\n  /** @type {ErrorInfo} */\n  disallowedContentInNoscriptInHead: {\n    reason: 'Disallowed content inside `<noscript>` in `<head>`',\n    description:\n      'Unexpected text character `%c`. Only use text in `<noscript>`s in `<body>`',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  duplicateAttribute: {\n    reason: 'Unexpected duplicate attribute',\n    description:\n      'Unexpectedly double attribute. Expected attributes to occur only once'\n  },\n  /** @type {ErrorInfo} */\n  endTagWithAttributes: {\n    reason: 'Unexpected attribute on closing tag',\n    description: 'Unexpected attribute. Expected `>` instead'\n  },\n  /** @type {ErrorInfo} */\n  endTagWithTrailingSolidus: {\n    reason: 'Unexpected slash at end of closing tag',\n    description: 'Unexpected `%c-1`. Expected `>` instead'\n  },\n  /** @type {ErrorInfo} */\n  endTagWithoutMatchingOpenElement: {\n    reason: 'Unexpected unopened end tag',\n    description: 'Unexpected end tag. Expected no end tag or another end tag',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  eofBeforeTagName: {\n    reason: 'Unexpected end of file',\n    description: 'Unexpected end of file. Expected tag name instead'\n  },\n  /** @type {ErrorInfo} */\n  eofInCdata: {\n    reason: 'Unexpected end of file in CDATA',\n    description: 'Unexpected end of file. Expected `]]>` to close the CDATA'\n  },\n  /** @type {ErrorInfo} */\n  eofInComment: {\n    reason: 'Unexpected end of file in comment',\n    description: 'Unexpected end of file. Expected `-->` to close the comment'\n  },\n  /** @type {ErrorInfo} */\n  eofInDoctype: {\n    reason: 'Unexpected end of file in doctype',\n    description:\n      'Unexpected end of file. Expected a valid doctype (such as `<!doctype html>`)'\n  },\n  /** @type {ErrorInfo} */\n  eofInElementThatCanContainOnlyText: {\n    reason: 'Unexpected end of file in element that can only contain text',\n    description: 'Unexpected end of file. Expected text or a closing tag',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  eofInScriptHtmlCommentLikeText: {\n    reason: 'Unexpected end of file in comment inside script',\n    description: 'Unexpected end of file. Expected `-->` to close the comment'\n  },\n  /** @type {ErrorInfo} */\n  eofInTag: {\n    reason: 'Unexpected end of file in tag',\n    description: 'Unexpected end of file. Expected `>` to close the tag'\n  },\n  /** @type {ErrorInfo} */\n  incorrectlyClosedComment: {\n    reason: 'Incorrectly closed comment',\n    description: 'Unexpected `%c-1`. Expected `-->` to close the comment'\n  },\n  /** @type {ErrorInfo} */\n  incorrectlyOpenedComment: {\n    reason: 'Incorrectly opened comment',\n    description: 'Unexpected `%c`. Expected `<!--` to open the comment'\n  },\n  /** @type {ErrorInfo} */\n  invalidCharacterSequenceAfterDoctypeName: {\n    reason: 'Invalid sequence after doctype name',\n    description: 'Unexpected sequence at `%c`. Expected `public` or `system`'\n  },\n  /** @type {ErrorInfo} */\n  invalidFirstCharacterOfTagName: {\n    reason: 'Invalid first character in tag name',\n    description: 'Unexpected `%c`. Expected an ASCII letter instead'\n  },\n  /** @type {ErrorInfo} */\n  misplacedDoctype: {\n    reason: 'Misplaced doctype',\n    description: 'Unexpected doctype. Expected doctype before head',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  misplacedStartTagForHeadElement: {\n    reason: 'Misplaced `<head>` start tag',\n    description:\n      'Unexpected start tag `<head>`. Expected `<head>` directly after doctype',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  missingAttributeValue: {\n    reason: 'Missing attribute value',\n    description:\n      'Unexpected `%c-1`. Expected an attribute value or no `%c-1` instead'\n  },\n  /** @type {ErrorInfo} */\n  missingDoctype: {\n    reason: 'Missing doctype before other content',\n    description: 'Expected a `<!doctype html>` before anything else',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  missingDoctypeName: {\n    reason: 'Missing doctype name',\n    description: 'Unexpected doctype end at `%c`. Expected `html` instead'\n  },\n  /** @type {ErrorInfo} */\n  missingDoctypePublicIdentifier: {\n    reason: 'Missing public identifier in doctype',\n    description: 'Unexpected `%c`. Expected identifier for `public` instead'\n  },\n  /** @type {ErrorInfo} */\n  missingDoctypeSystemIdentifier: {\n    reason: 'Missing system identifier in doctype',\n    description:\n      'Unexpected `%c`. Expected identifier for `system` instead (suggested: `\"about:legacy-compat\"`)'\n  },\n  /** @type {ErrorInfo} */\n  missingEndTagName: {\n    reason: 'Missing name in end tag',\n    description: 'Unexpected `%c`. Expected an ASCII letter instead'\n  },\n  /** @type {ErrorInfo} */\n  missingQuoteBeforeDoctypePublicIdentifier: {\n    reason: 'Missing quote before public identifier in doctype',\n    description: 'Unexpected `%c`. Expected `\"` or `\\'` instead'\n  },\n  /** @type {ErrorInfo} */\n  missingQuoteBeforeDoctypeSystemIdentifier: {\n    reason: 'Missing quote before system identifier in doctype',\n    description: 'Unexpected `%c`. Expected `\"` or `\\'` instead'\n  },\n  /** @type {ErrorInfo} */\n  missingSemicolonAfterCharacterReference: {\n    reason: 'Missing semicolon after character reference',\n    description: 'Unexpected `%c`. Expected `;` instead'\n  },\n  /** @type {ErrorInfo} */\n  missingWhitespaceAfterDoctypePublicKeyword: {\n    reason: 'Missing whitespace after public identifier in doctype',\n    description: 'Unexpected `%c`. Expected ASCII whitespace instead'\n  },\n  /** @type {ErrorInfo} */\n  missingWhitespaceAfterDoctypeSystemKeyword: {\n    reason: 'Missing whitespace after system identifier in doctype',\n    description: 'Unexpected `%c`. Expected ASCII whitespace instead'\n  },\n  /** @type {ErrorInfo} */\n  missingWhitespaceBeforeDoctypeName: {\n    reason: 'Missing whitespace before doctype name',\n    description: 'Unexpected `%c`. Expected ASCII whitespace instead'\n  },\n  /** @type {ErrorInfo} */\n  missingWhitespaceBetweenAttributes: {\n    reason: 'Missing whitespace between attributes',\n    description: 'Unexpected `%c`. Expected ASCII whitespace instead'\n  },\n  /** @type {ErrorInfo} */\n  missingWhitespaceBetweenDoctypePublicAndSystemIdentifiers: {\n    reason:\n      'Missing whitespace between public and system identifiers in doctype',\n    description: 'Unexpected `%c`. Expected ASCII whitespace instead'\n  },\n  /** @type {ErrorInfo} */\n  nestedComment: {\n    reason: 'Unexpected nested comment',\n    description: 'Unexpected `<!--`. Expected `-->`'\n  },\n  /** @type {ErrorInfo} */\n  nestedNoscriptInHead: {\n    reason: 'Unexpected nested `<noscript>` in `<head>`',\n    description:\n      'Unexpected `<noscript>`. Expected a closing tag or a meta element',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  nonConformingDoctype: {\n    reason: 'Unexpected non-conforming doctype declaration',\n    description:\n      'Expected `<!doctype html>` or `<!doctype html system \"about:legacy-compat\">`',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  nonVoidHtmlElementStartTagWithTrailingSolidus: {\n    reason: 'Unexpected trailing slash on start tag of non-void element',\n    description: 'Unexpected `/`. Expected `>` instead'\n  },\n  /** @type {ErrorInfo} */\n  noncharacterCharacterReference: {\n    reason:\n      'Unexpected noncharacter code point referenced by character reference',\n    description: 'Unexpected code point. Do not use noncharacters in HTML'\n  },\n  /** @type {ErrorInfo} */\n  noncharacterInInputStream: {\n    reason: 'Unexpected noncharacter character',\n    description: 'Unexpected code point `%x`. Do not use noncharacters in HTML'\n  },\n  /** @type {ErrorInfo} */\n  nullCharacterReference: {\n    reason: 'Unexpected NULL character referenced by character reference',\n    description: 'Unexpected code point. Do not use NULL characters in HTML'\n  },\n  /** @type {ErrorInfo} */\n  openElementsLeftAfterEof: {\n    reason: 'Unexpected end of file',\n    description: 'Unexpected end of file. Expected closing tag instead',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  surrogateCharacterReference: {\n    reason: 'Unexpected surrogate character referenced by character reference',\n    description:\n      'Unexpected code point. Do not use lone surrogate characters in HTML'\n  },\n  /** @type {ErrorInfo} */\n  surrogateInInputStream: {\n    reason: 'Unexpected surrogate character',\n    description:\n      'Unexpected code point `%x`. Do not use lone surrogate characters in HTML'\n  },\n  /** @type {ErrorInfo} */\n  unexpectedCharacterAfterDoctypeSystemIdentifier: {\n    reason: 'Invalid character after system identifier in doctype',\n    description: 'Unexpected character at `%c`. Expected `>`'\n  },\n  /** @type {ErrorInfo} */\n  unexpectedCharacterInAttributeName: {\n    reason: 'Unexpected character in attribute name',\n    description:\n      'Unexpected `%c`. Expected whitespace, `/`, `>`, `=`, or probably an ASCII letter'\n  },\n  /** @type {ErrorInfo} */\n  unexpectedCharacterInUnquotedAttributeValue: {\n    reason: 'Unexpected character in unquoted attribute value',\n    description: 'Unexpected `%c`. Quote the attribute value to include it'\n  },\n  /** @type {ErrorInfo} */\n  unexpectedEqualsSignBeforeAttributeName: {\n    reason: 'Unexpected equals sign before attribute name',\n    description: 'Unexpected `%c`. Add an attribute name before it'\n  },\n  /** @type {ErrorInfo} */\n  unexpectedNullCharacter: {\n    reason: 'Unexpected NULL character',\n    description:\n      'Unexpected code point `%x`. Do not use NULL characters in HTML'\n  },\n  /** @type {ErrorInfo} */\n  unexpectedQuestionMarkInsteadOfTagName: {\n    reason: 'Unexpected question mark instead of tag name',\n    description: 'Unexpected `%c`. Expected an ASCII letter instead'\n  },\n  /** @type {ErrorInfo} */\n  unexpectedSolidusInTag: {\n    reason: 'Unexpected slash in tag',\n    description:\n      'Unexpected `%c-1`. Expected it followed by `>` or in a quoted attribute value'\n  },\n  /** @type {ErrorInfo} */\n  unknownNamedCharacterReference: {\n    reason: 'Unexpected unknown named character reference',\n    description:\n      'Unexpected character reference. Expected known named character references'\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-from-html/lib/errors.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-from-html/lib/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/hast-util-from-html/lib/index.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromHtml: () => (/* binding */ fromHtml)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var hast_util_from_parse5__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-from-parse5 */ \"(ssr)/./node_modules/hast-util-from-parse5/lib/index.js\");\n/* harmony import */ var parse5__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! parse5 */ \"(ssr)/./node_modules/parse5/dist/index.js\");\n/* harmony import */ var vfile__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! vfile */ \"(ssr)/./node_modules/vfile/lib/index.js\");\n/* harmony import */ var vfile_message__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! vfile-message */ \"(ssr)/./node_modules/vfile-message/lib/index.js\");\n/* harmony import */ var _errors_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./errors.js */ \"(ssr)/./node_modules/hast-util-from-html/lib/errors.js\");\n/**\n * @import {Root} from 'hast'\n * @import {ParserError} from 'parse5'\n * @import {Value} from 'vfile'\n * @import {ErrorCode, Options} from './types.js'\n */\n\n\n\n\n\n\n\n\nconst base = 'https://html.spec.whatwg.org/multipage/parsing.html#parse-error-'\n\nconst dashToCamelRe = /-[a-z]/g\nconst formatCRe = /%c(?:([-+])(\\d+))?/g\nconst formatXRe = /%x/g\n\nconst fatalities = {2: true, 1: false, 0: null}\n\n/** @type {Readonly<Options>} */\nconst emptyOptions = {}\n\n/**\n * Turn serialized HTML into a hast tree.\n *\n * @param {VFile | Value} value\n *   Serialized HTML to parse.\n * @param {Readonly<Options> | null | undefined} [options]\n *   Configuration (optional).\n * @returns {Root}\n *   Tree.\n */\nfunction fromHtml(value, options) {\n  const settings = options || emptyOptions\n  const onerror = settings.onerror\n  const file = value instanceof vfile__WEBPACK_IMPORTED_MODULE_1__.VFile ? value : new vfile__WEBPACK_IMPORTED_MODULE_1__.VFile(value)\n  const parseFunction = settings.fragment ? parse5__WEBPACK_IMPORTED_MODULE_0__.parseFragment : parse5__WEBPACK_IMPORTED_MODULE_0__.parse\n  const document = String(file)\n  const p5Document = parseFunction(document, {\n    sourceCodeLocationInfo: true,\n    // Note `parse5` types currently do not allow `undefined`.\n    onParseError: settings.onerror ? internalOnerror : null,\n    scriptingEnabled: false\n  })\n\n  // `parse5` returns document which are always mapped to roots.\n  return /** @type {Root} */ (\n    (0,hast_util_from_parse5__WEBPACK_IMPORTED_MODULE_2__.fromParse5)(p5Document, {\n      file,\n      space: settings.space,\n      verbose: settings.verbose\n    })\n  )\n\n  /**\n   * Handle a parse error.\n   *\n   * @param {ParserError} error\n   *   Parse5 error.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function internalOnerror(error) {\n    const code = error.code\n    const name = camelcase(code)\n    const setting = settings[name]\n    const config = setting === null || setting === undefined ? true : setting\n    const level = typeof config === 'number' ? config : config ? 1 : 0\n\n    if (level) {\n      const info = _errors_js__WEBPACK_IMPORTED_MODULE_3__.errors[name]\n      ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(info, 'expected known error from `parse5`')\n\n      const message = new vfile_message__WEBPACK_IMPORTED_MODULE_5__.VFileMessage(format(info.reason), {\n        place: {\n          start: {\n            line: error.startLine,\n            column: error.startCol,\n            offset: error.startOffset\n          },\n          end: {\n            line: error.endLine,\n            column: error.endCol,\n            offset: error.endOffset\n          }\n        },\n        ruleId: code,\n        source: 'hast-util-from-html'\n      })\n\n      if (file.path) {\n        message.file = file.path\n        message.name = file.path + ':' + message.name\n      }\n\n      message.fatal = fatalities[level]\n      message.note = format(info.description)\n      message.url = info.url === false ? undefined : base + code\n\n      ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(onerror, '`internalOnerror` is not passed if `onerror` is not set')\n      onerror(message)\n    }\n\n    /**\n     * Format a human readable string about an error.\n     *\n     * @param {string} value\n     *   Value to format.\n     * @returns {string}\n     *   Formatted.\n     */\n    function format(value) {\n      return value.replace(formatCRe, formatC).replace(formatXRe, formatX)\n\n      /**\n       * Format the character.\n       *\n       * @param {string} _\n       *   Match.\n       * @param {string} $1\n       *   Sign (`-` or `+`, optional).\n       * @param {string} $2\n       *   Offset.\n       * @returns {string}\n       *   Formatted.\n       */\n      function formatC(_, $1, $2) {\n        const offset =\n          ($2 ? Number.parseInt($2, 10) : 0) * ($1 === '-' ? -1 : 1)\n        const char = document.charAt(error.startOffset + offset)\n        return visualizeCharacter(char)\n      }\n\n      /**\n       * Format the character code.\n       *\n       * @returns {string}\n       *   Formatted.\n       */\n      function formatX() {\n        return visualizeCharacterCode(document.charCodeAt(error.startOffset))\n      }\n    }\n  }\n}\n\n/**\n * @param {string} value\n *   Error code in dash case.\n * @returns {ErrorCode}\n *   Error code in camelcase.\n */\nfunction camelcase(value) {\n  // This should match an error code.\n  return /** @type {ErrorCode} */ (value.replace(dashToCamelRe, dashToCamel))\n}\n\n/**\n * @param {string} $0\n *   Match.\n * @returns {string}\n *   Camelcased.\n */\nfunction dashToCamel($0) {\n  return $0.charAt(1).toUpperCase()\n}\n\n/**\n * @param {string} char\n *   Character.\n * @returns {string}\n *   Formatted.\n */\nfunction visualizeCharacter(char) {\n  return char === '`' ? '` ` `' : char\n}\n\n/**\n * @param {number} charCode\n *   Character code.\n * @returns {string}\n *   Formatted.\n */\nfunction visualizeCharacterCode(charCode) {\n  return '0x' + charCode.toString(16).toUpperCase()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-from-html/lib/index.js\n");

/***/ })

};
;