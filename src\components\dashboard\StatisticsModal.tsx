'use client';

import { useState, useEffect } from 'react';
import { MessageStatistics, StatCard } from '@/lib/types/statistics';
import { ChatMessage } from '@/lib/types/chat';
import statisticsService from '@/lib/services/statisticsService';

interface StatisticsModalProps {
  isOpen: boolean;
  onClose: () => void;
  messages: ChatMessage[];
  chatName?: string;
}

export default function StatisticsModal({
  isOpen,
  onClose,
  messages,
  chatName = 'Todas as Conversas'
}: StatisticsModalProps) {
  const [statistics, setStatistics] = useState<MessageStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'details' | 'charts'>('overview');

  useEffect(() => {
    if (isOpen && messages.length > 0) {
      setLoading(true);
      try {
        const stats = statisticsService.calculateMessageStatistics(messages);
        setStatistics(stats);
      } catch (error) {
        console.error('Erro ao calcular estatísticas:', error);
      } finally {
        setLoading(false);
      }
    }
  }, [isOpen, messages]);

  if (!isOpen) return null;

  const formatNumber = (num: number): string => {
    return new Intl.NumberFormat('pt-BR').format(Math.round(num));
  };

  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 4
    }).format(value);
  };

  const formatTime = (ms: number): string => {
    if (ms < 1000) return `${Math.round(ms)}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}min`;
  };

  const getStatCards = (): StatCard[] => {
    if (!statistics) return [];

    return [
      {
        title: 'Total de Mensagens',
        value: formatNumber(statistics.totalMessages),
        icon: 'message',
        color: 'blue'
      },
      {
        title: 'Total de Palavras',
        value: formatNumber(statistics.totalWords),
        icon: 'text',
        color: 'green'
      },
      {
        title: 'Palavras da IA',
        value: formatNumber(statistics.totalWordsAI),
        icon: 'robot',
        color: 'purple'
      },
      {
        title: 'Palavras do Usuário',
        value: formatNumber(statistics.totalWordsUser),
        icon: 'user',
        color: 'orange'
      },

      {
        title: 'Tempo Médio de Resposta',
        value: formatTime(statistics.averageResponseTime),
        icon: 'clock',
        color: 'purple'
      },
      {
        title: 'Comprimento Médio',
        value: formatNumber(statistics.averageMessageLength),
        subtitle: 'caracteres',
        icon: 'length',
        color: 'orange'
      },
      {
        title: 'Palavras por Mensagem',
        value: formatNumber(statistics.averageWordsPerMessage),
        icon: 'average',
        color: 'cyan'
      },
      {
        title: 'Frases por Mensagem',
        value: formatNumber(statistics.averageSentencesPerMessage),
        icon: 'sentence',
        color: 'blue'
      },
      {
        title: 'Tempo de Leitura',
        value: `${statistics.estimatedReadingTime}min`,
        icon: 'read',
        color: 'green'
      },
      {
        title: 'Caracteres da IA',
        value: formatNumber(statistics.totalCharactersAI),
        icon: 'robot',
        color: 'purple'
      },
      {
        title: 'Caracteres do Usuário',
        value: formatNumber(statistics.totalCharactersUser),
        icon: 'user',
        color: 'orange'
      },
      {
        title: 'Total de Caracteres',
        value: formatNumber(statistics.totalCharacters),
        icon: 'total',
        color: 'cyan'
      }
    ];
  };

  const getIconSvg = (iconName: string) => {
    const icons: { [key: string]: string } = {
      message: 'M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z',
      text: 'M4 6h16M4 12h16M4 18h7',
      robot: 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z',
      user: 'M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z',
      input: 'M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z',
      output: 'M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14',
      dollar: 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1',
      clock: 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z',
      length: 'M7 16l-4-4m0 0l4-4m-4 4h18',
      average: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z',
      sentence: 'M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253',
      read: 'M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253',
      total: 'M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z'
    };
    return icons[iconName] || icons.message;
  };

  const getColorClasses = (color: StatCard['color']) => {
    const colors = {
      blue: 'from-blue-500/20 to-blue-600/20 border-blue-500/30 text-blue-300',
      green: 'from-green-500/20 to-green-600/20 border-green-500/30 text-green-300',
      purple: 'from-purple-500/20 to-purple-600/20 border-purple-500/30 text-purple-300',
      orange: 'from-orange-500/20 to-orange-600/20 border-orange-500/30 text-orange-300',
      red: 'from-red-500/20 to-red-600/20 border-red-500/30 text-red-300',
      cyan: 'from-cyan-500/20 to-cyan-600/20 border-cyan-500/30 text-cyan-300'
    };
    return colors[color];
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gradient-to-br from-blue-950/95 via-blue-900/95 to-blue-950/95 backdrop-blur-xl border border-blue-700/30 rounded-2xl shadow-2xl w-full max-w-7xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-blue-700/30">
          <div>
            <h2 className="text-2xl font-bold text-white flex items-center gap-3">
              <svg className="w-8 h-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              Estatísticas
            </h2>
            <p className="text-blue-300 mt-1">{chatName}</p>
          </div>
          <button
            onClick={onClose}
            className="text-blue-300 hover:text-white transition-colors p-2 hover:bg-blue-800/30 rounded-lg"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-blue-700/30">
          {[
            { id: 'overview', label: 'Visão Geral', icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z' },
            { id: 'details', label: 'Detalhes', icon: 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z' },
            { id: 'charts', label: 'Gráficos', icon: 'M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center gap-2 px-6 py-3 font-medium transition-all ${
                activeTab === tab.id
                  ? 'text-blue-300 border-b-2 border-blue-400 bg-blue-900/20'
                  : 'text-blue-400 hover:text-blue-300 hover:bg-blue-900/10'
              }`}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={tab.icon} />
              </svg>
              {tab.label}
            </button>
          ))}
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400"></div>
              <span className="ml-3 text-blue-300">Calculando estatísticas...</span>
            </div>
          ) : !statistics ? (
            <div className="text-center py-12">
              <p className="text-blue-300">Nenhuma estatística disponível</p>
            </div>
          ) : (
            <>
              {activeTab === 'overview' && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                  {getStatCards().map((card, index) => (
                    <div
                      key={index}
                      className={`bg-gradient-to-br ${getColorClasses(card.color)} border rounded-xl p-4 hover:scale-105 transition-transform duration-200`}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={getIconSvg(card.icon)} />
                        </svg>
                      </div>
                      <h3 className="text-sm font-medium text-white/80 mb-1">{card.title}</h3>
                      <p className="text-2xl font-bold text-white">{card.value}</p>
                      {card.subtitle && (
                        <p className="text-xs text-white/60 mt-1">{card.subtitle}</p>
                      )}
                    </div>
                  ))}
                </div>
              )}

              {activeTab === 'details' && (
                <div className="space-y-6">
                  {/* Palavras mais usadas */}
                  <div className="bg-blue-900/20 border border-blue-700/30 rounded-xl p-6">
                    <h3 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
                      <svg className="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                      </svg>
                      Palavras Mais Usadas
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {statistics.mostUsedWords.slice(0, 9).map((word, index) => (
                        <div key={index} className="bg-blue-800/30 rounded-lg p-4">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-white font-medium">#{index + 1}</span>
                            <span className="text-blue-300 text-sm">{word.percentage.toFixed(1)}%</span>
                          </div>
                          <p className="text-lg font-semibold text-white">{word.word}</p>
                          <p className="text-blue-400 text-sm">{formatNumber(word.count)} vezes</p>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Distribuição de mensagens */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="bg-blue-900/20 border border-blue-700/30 rounded-xl p-6">
                      <h3 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
                        <svg className="w-6 h-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        Distribuição por Usuário
                      </h3>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <span className="text-blue-300">Mensagens do Usuário</span>
                          <span className="text-white font-semibold">
                            {formatNumber(messages.filter(m => m.role === 'user').length)}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-blue-300">Mensagens da IA</span>
                          <span className="text-white font-semibold">
                            {formatNumber(messages.filter(m => m.role === 'assistant').length)}
                          </span>
                        </div>
                        <div className="w-full bg-blue-800/30 rounded-full h-3">
                          <div
                            className="bg-gradient-to-r from-green-500 to-blue-500 h-3 rounded-full transition-all duration-500"
                            style={{
                              width: `${(messages.filter(m => m.role === 'user').length / messages.length) * 100}%`
                            }}
                          ></div>
                        </div>
                      </div>
                    </div>

                    <div className="bg-blue-900/20 border border-blue-700/30 rounded-xl p-6">
                      <h3 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
                        <svg className="w-6 h-6 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                        </svg>
                        Análise de Custos
                      </h3>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <span className="text-blue-300">Custo por Mensagem</span>
                          <span className="text-white font-semibold">
                            {formatCurrency(statistics.totalCost / statistics.totalMessages)}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-blue-300">Custo por Token</span>
                          <span className="text-white font-semibold">
                            {formatCurrency(statistics.totalCost / (statistics.totalPromptTokens + statistics.totalCompletionTokens))}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-blue-300">Custo por Palavra</span>
                          <span className="text-white font-semibold">
                            {formatCurrency(statistics.totalCost / statistics.totalWords)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'charts' && (
                <div className="space-y-6">
                  <div className="text-center py-12">
                    <svg className="w-16 h-16 text-blue-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <h3 className="text-xl font-semibold text-white mb-2">Gráficos em Desenvolvimento</h3>
                    <p className="text-blue-300">
                      Os gráficos interativos estarão disponíveis em breve para uma melhor visualização dos dados.
                    </p>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}
