/**
 * Utilitário para testar a sincronização de favoritos
 * Este arquivo pode ser usado para debug e testes manuais
 */

export const testFavoritesSync = {
  // Simular um delay de rede
  simulateNetworkDelay: (ms: number = 1000) => {
    return new Promise(resolve => setTimeout(resolve, ms));
  },

  // Verificar se um modelo está marcado como favorito na UI
  checkModelFavoriteStatus: (modelId: string): boolean => {
    const modelCard = document.querySelector(`[data-model-id="${modelId}"]`);
    if (!modelCard) {
      console.warn(`Model card not found for ${modelId}`);
      return false;
    }

    const favoriteButton = modelCard.querySelector('[title*="favorito"]');
    if (!favoriteButton) {
      console.warn(`Favorite button not found for ${modelId}`);
      return false;
    }

    // Verificar se tem a classe de favorito ativo
    const hasYellowColor = favoriteButton.classList.contains('text-yellow-400');
    const hasYellowBg = favoriteButton.classList.contains('bg-yellow-500/20');
    
    return hasYellowColor && hasYellowBg;
  },

  // Simular clique no botão de favorito
  simulateFavoriteClick: (modelId: string): boolean => {
    const modelCard = document.querySelector(`[data-model-id="${modelId}"]`);
    if (!modelCard) {
      console.warn(`Model card not found for ${modelId}`);
      return false;
    }

    const favoriteButton = modelCard.querySelector('[title*="favorito"]') as HTMLButtonElement;
    if (!favoriteButton) {
      console.warn(`Favorite button not found for ${modelId}`);
      return false;
    }

    favoriteButton.click();
    return true;
  },

  // Verificar se há inconsistências visuais
  checkForVisualInconsistencies: (): string[] => {
    const issues: string[] = [];
    
    // Verificar todos os botões de favorito
    const favoriteButtons = document.querySelectorAll('[title*="favorito"]');
    
    favoriteButtons.forEach((button, index) => {
      const isYellow = button.classList.contains('text-yellow-400');
      const hasYellowBg = button.classList.contains('bg-yellow-500/20');
      const isBlue = button.classList.contains('text-blue-300');
      const hasBlueBg = button.classList.contains('bg-blue-800/30');

      // Verificar se está em estado inconsistente
      if ((isYellow && !hasYellowBg) || (!isYellow && hasYellowBg)) {
        issues.push(`Button ${index}: Inconsistent yellow state`);
      }

      if ((isBlue && !hasBlueBg) || (!isBlue && hasBlueBg)) {
        issues.push(`Button ${index}: Inconsistent blue state`);
      }

      // Verificar se tem ambos os estados (erro)
      if ((isYellow && isBlue) || (hasYellowBg && hasBlueBg)) {
        issues.push(`Button ${index}: Has both yellow and blue states`);
      }
    });

    return issues;
  },

  // Executar teste completo
  runFullTest: async (modelId: string): Promise<{
    success: boolean;
    steps: string[];
    issues: string[];
  }> => {
    const steps: string[] = [];
    const issues: string[] = [];

    try {
      // 1. Verificar estado inicial
      const initialState = testFavoritesSync.checkModelFavoriteStatus(modelId);
      steps.push(`Initial state: ${initialState ? 'favorited' : 'not favorited'}`);

      // 2. Simular clique
      const clickSuccess = testFavoritesSync.simulateFavoriteClick(modelId);
      if (!clickSuccess) {
        issues.push('Failed to simulate click');
        return { success: false, steps, issues };
      }
      steps.push('Click simulated');

      // 3. Verificar mudança imediata (deve ser instantânea com atualização otimista)
      await new Promise(resolve => setTimeout(resolve, 50)); // Pequeno delay para re-render
      const immediateState = testFavoritesSync.checkModelFavoriteStatus(modelId);
      const expectedImmediateState = !initialState;
      
      if (immediateState !== expectedImmediateState) {
        issues.push(`Immediate state incorrect: expected ${expectedImmediateState}, got ${immediateState}`);
      } else {
        steps.push('Immediate state change: ✓');
      }

      // 4. Aguardar operação no Firestore
      await testFavoritesSync.simulateNetworkDelay(2000);

      // 5. Verificar estado final
      const finalState = testFavoritesSync.checkModelFavoriteStatus(modelId);
      if (finalState !== expectedImmediateState) {
        issues.push(`Final state incorrect: expected ${expectedImmediateState}, got ${finalState}`);
      } else {
        steps.push('Final state consistent: ✓');
      }

      // 6. Verificar inconsistências visuais
      const visualIssues = testFavoritesSync.checkForVisualInconsistencies();
      if (visualIssues.length > 0) {
        issues.push(...visualIssues);
      } else {
        steps.push('No visual inconsistencies: ✓');
      }

      return {
        success: issues.length === 0,
        steps,
        issues
      };

    } catch (error) {
      issues.push(`Test error: ${error}`);
      return { success: false, steps, issues };
    }
  }
};

// Expor globalmente para uso no console do navegador
if (typeof window !== 'undefined') {
  (window as any).testFavoritesSync = testFavoritesSync;
}
