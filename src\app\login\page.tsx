'use client';

import { useState, useEffect } from 'react';
import { signInWithEmailAndPassword, createUserWithEmailAndPassword } from 'firebase/auth';
import { doc, setDoc } from 'firebase/firestore';
import { ref, uploadBytes } from 'firebase/storage';
import { auth, db, storage } from '@/lib/firebase';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';

export default function LoginPage() {
  const [mode, setMode] = useState<'login' | 'register'>('login');
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();

  // Redirecionar se já estiver logado
  useEffect(() => {
    if (!authLoading && user) {
      router.push('/');
    }
  }, [user, authLoading, router]);

  // Mostrar loading enquanto verifica autenticação
  if (authLoading) {
    return (
      <div className="min-h-screen bg-gradient-rafthor flex flex-col items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
          <p className="text-white/80">Carregando...</p>
        </div>
      </div>
    );
  }

  // Não renderizar se já estiver logado (será redirecionado)
  if (user) {
    return null;
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      if (mode === 'register') {
        // Validar se o nome de usuário foi preenchido
        if (!formData.username.trim()) {
          setError('Nome de usuário é obrigatório');
          return;
        }

        // Criar usuário no Firebase Auth
        console.log('Criando usuário no Firebase Auth...');
        const userCredential = await createUserWithEmailAndPassword(
          auth,
          formData.email,
          formData.password
        );
        console.log('Usuário criado no Auth:', userCredential.user.uid);

        // Criar documento no Firestore
        console.log('Criando documento no Firestore...');
        const userData = {
          id: userCredential.user.uid,
          username: formData.username,
          email: formData.email,
          balance: 0,
          createdAt: new Date().toISOString()
        };

        console.log('Dados do usuário:', userData);
        console.log('Caminho do documento:', `usuarios/${formData.username}`);

        await setDoc(doc(db, 'usuarios', formData.username), userData);
        console.log('Documento criado no Firestore com sucesso!');

        // Criar configurações padrão
        console.log('Criando configurações padrão...');
        const defaultConfigs = {
          aparencia: {
            fonte: 'Inter',
            tamanhoFonte: 14,
            palavrasPorSessao: 5000
          },
          endpoints: {
            'OpenRouter': {
              nome: 'OpenRouter',
              url: 'https://openrouter.ai/api/v1/chat/completions',
              apiKey: '',
              modeloPadrao: 'meta-llama/llama-3.1-8b-instruct:free',
              ativo: false
            },
            'DeepSeek': {
              nome: 'DeepSeek',
              url: 'https://api.deepseek.com/v1/chat/completions',
              apiKey: '',
              modeloPadrao: 'deepseek-chat',
              ativo: false
            }
          },
          memorias: {},
          categorias: {}
        };

        await setDoc(doc(db, 'usuarios', formData.username, 'configuracoes', 'settings'), defaultConfigs);
        console.log('Configurações padrão criadas com sucesso!');

        // Criar estrutura de pastas no Storage
        try {
          console.log('Criando estrutura de pastas no Storage...');
          const userFolderRef = ref(storage, `usuarios/${formData.username}/.keep`);
          const emptyFile = new Uint8Array(0);
          await uploadBytes(userFolderRef, emptyFile);
          console.log('Estrutura de pastas criada no Storage com sucesso!');
        } catch (storageError) {
          console.error('Erro ao criar estrutura no Storage:', storageError);
          // Não bloquear o registro se houver erro no Storage
        }

        alert('Usuário registrado com sucesso! Verifique o Firestore.');
        router.push('/');
      } else {
        // Login
        await signInWithEmailAndPassword(auth, formData.email, formData.password);
        console.log('Login realizado com sucesso!');
        router.push('/');
      }
    } catch (error: any) {
      console.error('Erro completo:', error);
      console.error('Código do erro:', error.code);
      console.error('Mensagem do erro:', error.message);

      // Traduzir erros do Firebase para português
      let errorMessage = 'Erro ao processar solicitação';

      if (error.code === 'auth/email-already-in-use') {
        errorMessage = 'Este email já está em uso';
      } else if (error.code === 'auth/weak-password') {
        errorMessage = 'A senha deve ter pelo menos 6 caracteres';
      } else if (error.code === 'auth/invalid-email') {
        errorMessage = 'Email inválido';
      } else if (error.code === 'auth/user-not-found') {
        errorMessage = 'Usuário não encontrado';
      } else if (error.code === 'auth/wrong-password') {
        errorMessage = 'Senha incorreta';
      } else if (error.code === 'auth/invalid-credential') {
        errorMessage = 'Credenciais inválidas';
      } else if (error.code === 'permission-denied') {
        errorMessage = 'Permissão negada para escrever no Firestore. Verifique as regras de segurança.';
      } else if (error.code === 'unavailable') {
        errorMessage = 'Serviço do Firestore indisponível. Tente novamente.';
      } else {
        errorMessage = `Erro: ${error.message}`;
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const toggleMode = () => {
    setMode(mode === 'login' ? 'register' : 'login');
    setError('');
  };

  return (
    <div className="min-h-screen bg-gradient-rafthor relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0">
        {/* Animated gradient orbs */}
        <div className="absolute top-0 left-0 w-full h-full">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-white/5 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-white/5 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-white/3 rounded-full blur-3xl animate-pulse delay-500"></div>
        </div>
        
        {/* Grid pattern */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0" style={{
            backgroundImage: `radial-gradient(circle at 1px 1px, rgba(255,255,255,0.15) 1px, transparent 0)`,
            backgroundSize: '20px 20px'
          }}></div>
        </div>
      </div>

      {/* Header */}
      <div className="relative z-10 flex items-center justify-between p-4 sm:p-8">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-white/10 rounded-xl flex items-center justify-center backdrop-blur-sm border border-white/20">
            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </div>
          <div>
            <h1 className="text-xl sm:text-2xl font-bold text-white">Rafthor</h1>
            <p className="text-white/60 text-xs sm:text-sm">AI Chatbot Platform</p>
          </div>
        </div>
        
        <div className="hidden sm:block">
          <div className="text-right">
            <p className="text-white/60 text-sm">Versão Beta</p>
            <p className="text-white/40 text-xs">v1.0.0</p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 flex items-center justify-center min-h-[calc(100vh-200px)] px-4 py-8">
        <div className="w-full max-w-md">
          <div className="w-full max-w-md mx-auto px-4 sm:px-0 animate-slide-in-up">
            {/* Card principal com glassmorphism */}
            <div className="relative bg-white/5 backdrop-blur-xl rounded-3xl p-6 sm:p-8 shadow-2xl border border-white/10 overflow-hidden hover-lift">
              {/* Efeitos de fundo animados */}
              <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-white/5"></div>
              <div className="absolute -top-20 -right-20 w-40 h-40 bg-white/5 rounded-full blur-3xl animate-pulse"></div>
              <div className="absolute -bottom-20 -left-20 w-40 h-40 bg-white/5 rounded-full blur-3xl animate-pulse delay-1000"></div>
              
              {/* Conteúdo */}
              <div className="relative z-10">
                {/* Header */}
                <div className="text-center mb-8">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-white/10 rounded-2xl mb-4 backdrop-blur-sm border border-white/20 animate-float">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <h1 className="text-2xl sm:text-3xl font-bold text-white mb-2 tracking-tight">
                    {mode === 'login' ? 'Bem-vindo de volta' : 'Criar sua conta'}
                  </h1>
                  <p className="text-white/60 text-sm sm:text-base">
                    {mode === 'login' 
                      ? 'Entre na sua conta do Rafthor' 
                      : 'Junte-se à revolução da IA'
                    }
                  </p>
                </div>

                {/* Formulário */}
                <form onSubmit={handleSubmit} className="space-y-5">
                  {mode === 'register' && (
                    <div className="group">
                      <label htmlFor="username" className="block text-sm font-semibold text-white/90 mb-3">
                        Nome de usuário
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                          <svg className="h-5 w-5 text-white/40 group-focus-within:text-white/70 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                          </svg>
                        </div>
                        <input
                          type="text"
                          id="username"
                          name="username"
                          value={formData.username}
                          onChange={handleInputChange}
                          required
                          className="w-full pl-12 pr-4 py-4 bg-white/5 border border-white/20 rounded-2xl text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-white/30 focus:bg-white/10 transition-all duration-300 text-sm sm:text-base"
                          placeholder="Escolha um nome de usuário"
                        />
                      </div>
                    </div>
                  )}

                  <div className="group">
                    <label htmlFor="email" className="block text-sm font-semibold text-white/90 mb-3">
                      {mode === 'login' ? 'Email' : 'Endereço de email'}
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                        <svg className="h-5 w-5 text-white/40 group-focus-within:text-white/70 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                        </svg>
                      </div>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        className="w-full pl-12 pr-4 py-4 bg-white/5 border border-white/20 rounded-2xl text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-white/30 focus:bg-white/10 transition-all duration-300 text-sm sm:text-base"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div className="group">
                    <label htmlFor="password" className="block text-sm font-semibold text-white/90 mb-3">
                      Senha
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                        <svg className="h-5 w-5 text-white/40 group-focus-within:text-white/70 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                        </svg>
                      </div>
                      <input
                        type={showPassword ? "text" : "password"}
                        id="password"
                        name="password"
                        value={formData.password}
                        onChange={handleInputChange}
                        required
                        className="w-full pl-12 pr-12 py-4 bg-white/5 border border-white/20 rounded-2xl text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-white/30 focus:bg-white/10 transition-all duration-300 text-sm sm:text-base"
                        placeholder="Crie uma senha segura"
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute inset-y-0 right-0 pr-4 flex items-center text-white/40 hover:text-white/70 transition-colors"
                      >
                        {showPassword ? (
                          <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                          </svg>
                        ) : (
                          <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                        )}
                      </button>
                    </div>
                  </div>

                  {/* Mensagem de erro */}
                  {error && (
                    <div className="bg-red-500/10 border border-red-500/30 rounded-2xl p-4 backdrop-blur-sm animate-fade-in">
                      <div className="flex items-center space-x-3">
                        <svg className="h-5 w-5 text-red-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <p className="text-red-200 text-sm">{error}</p>
                      </div>
                    </div>
                  )}

                  {/* Botão de submit */}
                  <button
                    type="submit"
                    disabled={loading}
                    className="group relative w-full bg-gradient-to-r from-white/20 to-white/10 hover:from-white/30 hover:to-white/20 text-white font-semibold py-4 px-6 rounded-2xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed backdrop-blur-sm border border-white/20 hover:border-white/30 transform hover:scale-[1.02] active:scale-[0.98]"
                  >
                    <div className="flex items-center justify-center space-x-3">
                      {loading ? (
                        <>
                          <svg className="animate-spin h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          <span>Processando...</span>
                        </>
                      ) : (
                        <>
                          <span>{mode === 'login' ? 'Entrar na conta' : 'Criar minha conta'}</span>
                          <svg className="h-5 w-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                          </svg>
                        </>
                      )}
                    </div>
                  </button>
                </form>

                {/* Divisor */}
                <div className="relative my-8">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-white/10"></div>
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-4 bg-gradient-rafthor text-white/50">
                      {mode === 'login' ? 'Novo por aqui?' : 'Já tem uma conta?'}
                    </span>
                  </div>
                </div>

                {/* Toggle mode */}
                <button
                  onClick={toggleMode}
                  className="w-full text-center py-3 px-4 rounded-2xl border border-white/20 text-white/80 hover:text-white hover:bg-white/5 hover:border-white/30 transition-all duration-300 font-medium backdrop-blur-sm"
                >
                  {mode === 'login' ? (
                    <span className="flex items-center justify-center space-x-2">
                      <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                      </svg>
                      <span>Criar nova conta</span>
                    </span>
                  ) : (
                    <span className="flex items-center justify-center space-x-2">
                      <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                      </svg>
                      <span>Entrar na minha conta</span>
                    </span>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="relative z-10 p-4 sm:p-8">
        <div className="text-center">
          <p className="text-white/40 text-xs sm:text-sm">
            © 2025 Rafthor. Plataforma de chatbot com múltiplas IAs.
          </p>
          <div className="flex items-center justify-center space-x-4 mt-2">
            <a href="#" className="text-white/30 hover:text-white/60 transition-colors text-xs">
              Privacidade
            </a>
            <span className="text-white/20">•</span>
            <a href="#" className="text-white/30 hover:text-white/60 transition-colors text-xs">
              Termos
            </a>
            <span className="text-white/20">•</span>
            <a href="#" className="text-white/30 hover:text-white/60 transition-colors text-xs">
              Suporte
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
