import { NextRequest, NextResponse } from 'next/server';
import { ref, getDownloadURL, uploadBytes } from 'firebase/storage';
import { storage } from '@/lib/firebase';

interface RouteParams {
  params: {
    username: string;
    chatId: string;
    messageId: string;
  };
}

// DELETE - Deletar mensagem
export async function DELETE(
  request: NextRequest,
  { params }: RouteParams
) {
  const { username, chatId, messageId } = params;

  if (!username || !chatId || !messageId) {
    return NextResponse.json(
      { error: 'Username, chatId e messageId são obrigatórios' },
      { status: 400 }
    );
  }

  try {
    // Referência para o arquivo chat.json no Firebase Storage
    const chatJsonRef = ref(storage, `usuarios/${username}/conversas/${chatId}/chat.json`);
    
    // Obter URL de download
    const downloadUrl = await getDownloadURL(chatJsonRef);
    
    // Fazer fetch do arquivo
    const response = await fetch(downloadUrl);
    
    if (!response.ok) {
      throw new Error(`Erro ao buscar arquivo: ${response.statusText}`);
    }

    const chatData = await response.json();

    // Filtrar mensagens removendo a mensagem com o ID especificado
    const updatedMessages = chatData.messages.filter((msg: any) => msg.id !== messageId);

    // Atualizar dados do chat
    const updatedChatData = {
      ...chatData,
      messages: updatedMessages,
      lastUpdated: new Date().toISOString()
    };

    // Salvar arquivo atualizado
    const chatJsonBlob = new Blob([JSON.stringify(updatedChatData, null, 2)], {
      type: 'application/json'
    });

    await uploadBytes(chatJsonRef, chatJsonBlob);

    return NextResponse.json(
      { success: true, message: 'Mensagem deletada com sucesso' },
      {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        },
      }
    );

  } catch (error) {
    console.error('Erro ao deletar mensagem:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

// PUT - Atualizar mensagem
export async function PUT(
  request: NextRequest,
  { params }: RouteParams
) {
  const { username, chatId, messageId } = params;

  if (!username || !chatId || !messageId) {
    return NextResponse.json(
      { error: 'Username, chatId e messageId são obrigatórios' },
      { status: 400 }
    );
  }

  try {
    const body = await request.json();
    const { content } = body;

    if (!content || typeof content !== 'string') {
      return NextResponse.json(
        { error: 'Conteúdo da mensagem é obrigatório' },
        { status: 400 }
      );
    }

    // Referência para o arquivo chat.json no Firebase Storage
    const chatJsonRef = ref(storage, `usuarios/${username}/conversas/${chatId}/chat.json`);
    
    // Obter URL de download
    const downloadUrl = await getDownloadURL(chatJsonRef);
    
    // Fazer fetch do arquivo
    const response = await fetch(downloadUrl);
    
    if (!response.ok) {
      throw new Error(`Erro ao buscar arquivo: ${response.statusText}`);
    }

    const chatData = await response.json();

    // Atualizar a mensagem específica
    const updatedMessages = chatData.messages.map((msg: any) => {
      if (msg.id === messageId) {
        return {
          ...msg,
          content: content.trim(),
          timestamp: new Date().toISOString()
        };
      }
      return msg;
    });

    // Atualizar dados do chat
    const updatedChatData = {
      ...chatData,
      messages: updatedMessages,
      lastUpdated: new Date().toISOString()
    };

    // Salvar arquivo atualizado
    const chatJsonBlob = new Blob([JSON.stringify(updatedChatData, null, 2)], {
      type: 'application/json'
    });

    await uploadBytes(chatJsonRef, chatJsonBlob);

    return NextResponse.json(
      { success: true, message: 'Mensagem atualizada com sucesso' },
      {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        },
      }
    );

  } catch (error) {
    console.error('Erro ao atualizar mensagem:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

// OPTIONS - Para CORS
export async function OPTIONS() {
  return NextResponse.json({}, {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
