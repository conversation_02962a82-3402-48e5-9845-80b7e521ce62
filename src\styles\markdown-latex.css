/* Estilos customizados para LaTeX no Markdown */

/* Melhorar a renderização de fórmulas inline */
.markdown-content .katex {
  font-size: 1.1em !important;
  color: #e2e8f0 !important; /* text-gray-200 */
}

/* Fórmulas em bloco */
.markdown-content .katex-display {
  margin: 1.5rem 0 !important;
  padding: 1rem !important;
  background: rgba(30, 58, 138, 0.1) !important; /* bg-blue-900/10 */
  border: 1px solid rgba(59, 130, 246, 0.3) !important; /* border-blue-500/30 */
  border-radius: 0.5rem !important;
  overflow-x: auto !important;
}

/* Melhorar a legibilidade das fórmulas */
.markdown-content .katex .base {
  color: #f1f5f9 !important; /* text-slate-100 */
}

/* Operadores matemáticos */
.markdown-content .katex .mbin,
.markdown-content .katex .mrel {
  color: #60a5fa !important; /* text-blue-400 */
}

/* Números e variáveis */
.markdown-content .katex .mord {
  color: #e2e8f0 !important; /* text-gray-200 */
}

/* Funções matemáticas */
.markdown-content .katex .mop {
  color: #34d399 !important; /* text-emerald-400 */
}

/* Parênteses e delimitadores */
.markdown-content .katex .mopen,
.markdown-content .katex .mclose {
  color: #fbbf24 !important; /* text-amber-400 */
}

/* Sobrescritos e subscritos */
.markdown-content .katex .msupsub {
  color: #c084fc !important; /* text-purple-400 */
}

/* Melhorar a compatibilidade com o tema escuro */
.markdown-content .katex .katex-html {
  color: #e2e8f0 !important;
}

/* Corrigir problemas de overflow em dispositivos móveis */
@media (max-width: 768px) {
  .markdown-content .katex-display {
    font-size: 0.9em !important;
    padding: 0.75rem !important;
    margin: 1rem 0 !important;
  }
  
  .markdown-content .katex {
    font-size: 1em !important;
  }
}

/* Melhorar a renderização de matrizes */
.markdown-content .katex .arraycolsep {
  width: 0.5em !important;
}

/* Estilos para frações */
.markdown-content .katex .frac-line {
  border-bottom-color: #60a5fa !important; /* border-blue-400 */
}

/* Melhorar a renderização de raízes */
.markdown-content .katex .sqrt > .root {
  color: #10b981 !important; /* text-emerald-500 */
}

/* Estilos para integrais e somatórios */
.markdown-content .katex .op-symbol {
  color: #f59e0b !important; /* text-amber-500 */
}

/* Corrigir problemas de alinhamento */
.markdown-content .katex-display > .katex {
  text-align: center !important;
}

/* Melhorar a renderização de texto em fórmulas */
.markdown-content .katex .text {
  color: #d1d5db !important; /* text-gray-300 */
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif !important;
}

/* Estilos para acentos e decorações */
.markdown-content .katex .accent > .accent-body {
  color: #8b5cf6 !important; /* text-violet-500 */
}

/* Melhorar a renderização de vetores */
.markdown-content .katex .overrightarrow > .arrow {
  color: #06b6d4 !important; /* text-cyan-500 */
}

/* Corrigir problemas com fontes matemáticas */
.markdown-content .katex .mathdefault {
  font-family: KaTeX_Main, "Times New Roman", serif !important;
}

/* Melhorar o contraste para melhor legibilidade */
.markdown-content .katex .katex-mathml {
  color: #f8fafc !important; /* text-slate-50 */
}

/* Estilos para casos especiais (como sistemas de equações) */
.markdown-content .katex .cases > .arraycolsep {
  width: 0.25em !important;
}

/* Melhorar a renderização de limites */
.markdown-content .katex .op-limits > .vlist-t {
  color: #f97316 !important; /* text-orange-500 */
}

/* Corrigir problemas de espaçamento em fórmulas complexas */
.markdown-content .katex .mspace {
  margin: 0 0.2em !important;
}

/* Estilos para melhorar a acessibilidade */
.markdown-content .katex[title] {
  cursor: help;
}

/* Melhorar a renderização em telas de alta resolução */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .markdown-content .katex {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}
