import { NextRequest, NextResponse } from 'next/server';

export interface OpenRouterModel {
  id: string;
  name: string;
  created: number;
  description: string;
  architecture: {
    input_modalities: string[];
    output_modalities: string[];
    tokenizer: string;
    instruct_type: string | null;
  };
  top_provider: {
    is_moderated: boolean;
    context_length: number | null;
    max_completion_tokens: number | null;
  };
  pricing: {
    prompt: string;
    completion: string;
    image: string;
    request: string;
    web_search: string;
    internal_reasoning: string;
    input_cache_read: string | null;
    input_cache_write: string | null;
  };
  canonical_slug: string | null;
  context_length: number | null;
  hugging_face_id: string | null;
  per_request_limits: object | null;
  supported_parameters: string[] | null;
}

export interface OpenRouterModelsResponse {
  data: OpenRouterModel[];
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');

    // Construir URL da API da OpenRouter
    let apiUrl = 'https://openrouter.ai/api/v1/models';
    if (category) {
      apiUrl += `?category=${encodeURIComponent(category)}`;
    }

    // Fazer requisição para a API da OpenRouter
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Erro da API OpenRouter: ${response.status} ${response.statusText}`);
    }

    const data: OpenRouterModelsResponse = await response.json();

    // Filtrar e organizar os modelos
    const processedModels = data.data.map(model => ({
      id: model.id,
      name: model.name,
      description: model.description,
      contextLength: model.context_length || model.top_provider.context_length,
      maxCompletionTokens: model.top_provider.max_completion_tokens,
      isModerated: model.top_provider.is_moderated,
      pricing: {
        prompt: parseFloat(model.pricing.prompt) || 0,
        completion: parseFloat(model.pricing.completion) || 0,
        isFree: model.pricing.prompt === '0' && model.pricing.completion === '0',
      },
      inputModalities: model.architecture.input_modalities,
      outputModalities: model.architecture.output_modalities,
      tokenizer: model.architecture.tokenizer,
      instructType: model.architecture.instruct_type,
      huggingFaceId: model.hugging_face_id,
      supportedParameters: model.supported_parameters,
    }));

    // Ordenar modelos: gratuitos primeiro, depois por popularidade (nome)
    const sortedModels = processedModels.sort((a, b) => {
      // Primeiro critério: modelos gratuitos primeiro
      if (a.pricing.isFree && !b.pricing.isFree) return -1;
      if (!a.pricing.isFree && b.pricing.isFree) return 1;
      
      // Segundo critério: ordenar por nome
      return a.name.localeCompare(b.name);
    });

    return NextResponse.json({
      success: true,
      models: sortedModels,
      total: sortedModels.length,
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
        // Cache por 1 hora
        'Cache-Control': 'public, max-age=3600',
      },
    });

  } catch (error) {
    console.error('Erro ao buscar modelos da OpenRouter:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Erro ao buscar modelos da OpenRouter',
      details: error instanceof Error ? error.message : 'Erro desconhecido',
    }, { 
      status: 500,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });
  }
}

// Suporte para preflight requests (OPTIONS)
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
