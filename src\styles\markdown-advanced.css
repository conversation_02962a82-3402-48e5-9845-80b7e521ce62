/* ===== ESTILOS AVANÇADOS PARA MARKDOWN ===== */

/* ===== ESTILOS PARA WEB SEARCH ===== */
.web-search-section {
  position: relative;
  overflow: hidden;
  animation: webSearchGlow 3s ease-in-out infinite alternate;
}

.web-search-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
  border-radius: 0.5rem;
  pointer-events: none;
}

.web-search-section > * {
  position: relative;
  z-index: 1;
}

/* Animação sutil para a seção de web search */
@keyframes webSearchGlow {
  0% {
    box-shadow: 0 0 5px rgba(6, 182, 212, 0.3);
  }
  100% {
    box-shadow: 0 0 15px rgba(6, 182, 212, 0.5), 0 0 25px rgba(59, 130, 246, 0.3);
  }
}

/* Estilização especial para links de web search no conteúdo */
.markdown-content a[href*="reuters.com"],
.markdown-content a[href*="g1.globo.com"],
.markdown-content a[href*="reliefweb.int"],
.markdown-content a[href*=".com"],
.markdown-content a[href*=".org"],
.markdown-content a[href*=".net"] {
  color: #06b6d4 !important;
  text-decoration: none !important;
  border-bottom: 1px solid rgba(6, 182, 212, 0.5) !important;
  transition: all 0.2s ease !important;
  padding: 0 2px !important;
  border-radius: 2px !important;
}

.markdown-content a[href*="reuters.com"]:hover,
.markdown-content a[href*="g1.globo.com"]:hover,
.markdown-content a[href*="reliefweb.int"]:hover,
.markdown-content a[href*=".com"]:hover,
.markdown-content a[href*=".org"]:hover,
.markdown-content a[href*=".net"]:hover {
  background: rgba(6, 182, 212, 0.1) !important;
  border-bottom-color: #06b6d4 !important;
  color: #67e8f9 !important;
}

/* ===== ANIMAÇÕES E TRANSIÇÕES ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: 200px 0;
  }
}

/* ===== ELEMENTOS INTERATIVOS ===== */
.markdown-content h1,
.markdown-content h2,
.markdown-content h3 {
  animation: fadeInUp 0.6s ease-out;
}

.markdown-content p {
  animation: slideInLeft 0.4s ease-out;
}

.markdown-content li {
  animation: fadeInUp 0.3s ease-out;
}

/* ===== DESABILITAR ANIMAÇÕES DURANTE STREAMING ===== */
.markdown-content.streaming-mode h1,
.markdown-content.streaming-mode h2,
.markdown-content.streaming-mode h3,
.markdown-content.streaming-mode p,
.markdown-content.streaming-mode li {
  animation: none !important;
}

.markdown-content.streaming-mode pre::after {
  animation: none !important;
}

/* ===== EFEITOS ESPECIAIS PARA CÓDIGO ===== */
.markdown-content pre {
  position: relative;
  overflow: hidden;
}

.markdown-content pre::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.05),
    transparent
  );
  animation: shimmer 3s infinite;
}

/* ===== TOOLTIPS PARA LINKS ===== */
.markdown-content a[title] {
  position: relative;
}

.markdown-content a[title]:hover::after {
  content: attr(title);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(13, 28, 74, 0.95);
  color: #e2e8f0;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  white-space: nowrap;
  z-index: 1000;
  border: 1px solid rgba(59, 130, 246, 0.3);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  animation: fadeInUp 0.2s ease-out;
}

.markdown-content a[title]:hover::before {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(1px);
  border: 4px solid transparent;
  border-top-color: rgba(13, 28, 74, 0.95);
  z-index: 1001;
}

/* ===== BADGES E TAGS ===== */
.markdown-content .badge {
  display: inline-block;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin: 0 0.25rem;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.markdown-content .badge.success {
  background: linear-gradient(135deg, #10b981, #059669);
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}

.markdown-content .badge.warning {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
}

.markdown-content .badge.error {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
}

/* ===== CALLOUTS E ALERTAS ===== */
.markdown-content .callout {
  margin: 1.5rem 0;
  padding: 1.25rem;
  border-radius: 0.75rem;
  border-left: 4px solid;
  position: relative;
  backdrop-filter: blur(10px);
}

.markdown-content .callout.info {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 197, 253, 0.05));
  border-left-color: #3b82f6;
  color: #dbeafe;
}

.markdown-content .callout.success {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(110, 231, 183, 0.05));
  border-left-color: #10b981;
  color: #d1fae5;
}

.markdown-content .callout.warning {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(251, 191, 36, 0.05));
  border-left-color: #f59e0b;
  color: #fef3c7;
}

.markdown-content .callout.error {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(252, 165, 165, 0.05));
  border-left-color: #ef4444;
  color: #fee2e2;
}

.markdown-content .callout::before {
  content: '';
  position: absolute;
  top: 1.25rem;
  left: -2px;
  width: 4px;
  height: 1.5rem;
  background: inherit;
  border-radius: 0 2px 2px 0;
}

/* ===== PROGRESS BARS ===== */
.markdown-content .progress {
  width: 100%;
  height: 0.5rem;
  background: rgba(30, 58, 138, 0.3);
  border-radius: 9999px;
  overflow: hidden;
  margin: 1rem 0;
  position: relative;
}

.markdown-content .progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #60a5fa);
  border-radius: 9999px;
  transition: width 0.3s ease;
  position: relative;
}

.markdown-content .progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: shimmer 2s infinite;
}

/* ===== SYNTAX HIGHLIGHTING MELHORADO ===== */
.markdown-content .hljs-title {
  color: #fbbf24 !important;
  font-weight: 600;
}

.markdown-content .hljs-attr {
  color: #60a5fa !important;
}

.markdown-content .hljs-built_in {
  color: #c084fc !important;
}

.markdown-content .hljs-type {
  color: #34d399 !important;
}

.markdown-content .hljs-literal {
  color: #f87171 !important;
}

.markdown-content .hljs-meta {
  color: #6b7280 !important;
}

.markdown-content .hljs-tag {
  color: #60a5fa !important;
}

.markdown-content .hljs-name {
  color: #fbbf24 !important;
}

.markdown-content .hljs-selector-id,
.markdown-content .hljs-selector-class {
  color: #34d399 !important;
}

/* ===== MELHORIAS PARA DISPOSITIVOS MÓVEIS ===== */
@media (max-width: 640px) {
  .markdown-content .callout {
    padding: 1rem;
    margin: 1rem 0;
  }
  
  .markdown-content table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }
  
  .markdown-content pre {
    font-size: 0.75rem;
    line-height: 1.4;
  }
  
  .markdown-content blockquote {
    padding: 0.75rem 1rem;
    margin: 1rem 0;
  }
  
  .markdown-content blockquote::before {
    font-size: 2rem;
    top: -0.25rem;
  }
}

/* ===== DARK MODE OPTIMIZATIONS ===== */
@media (prefers-color-scheme: dark) {
  .markdown-content {
    color: #e2e8f0;
  }
  
  .markdown-content code {
    background: rgba(30, 58, 138, 0.4);
  }
  
  .markdown-content pre {
    background: linear-gradient(135deg, rgba(13, 28, 74, 0.9), rgba(30, 58, 138, 0.7)) !important;
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  .markdown-content {
    color: #000;
    background: #fff;
  }
  
  .markdown-content pre {
    background: #f5f5f5 !important;
    border: 1px solid #ddd;
  }
  
  .markdown-content a {
    color: #0066cc;
    text-decoration: underline;
  }
  
  .markdown-content blockquote {
    background: #f9f9f9;
    border-left-color: #666;
  }
}
