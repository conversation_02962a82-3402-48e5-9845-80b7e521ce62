'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { doc, setDoc, collection, query, where, getDocs } from 'firebase/firestore';
import { ref, uploadBytes } from 'firebase/storage';
import { db, storage } from '@/lib/firebase';

interface ImportedChatData {
  id: string;
  name: string;
  context: string;
  system_prompt: string;
  temperature: number;
  maxTokens: number;
  frequency_penalty: number;
  repetition_penalty: number;
  createdAt: number;
  lastUpdatedAt: number;
  latexInstructions: boolean;
  messages: Array<{
    id: string;
    role: 'user' | 'assistant';
    content: string;
    timestamp: number;
    usage?: {
      prompt_tokens: number;
      completion_tokens: number;
      total_tokens: number;
      cost: number;
    };
    responseTime?: number;
  }>;
  lastUsedModel?: string;
}

interface ImportResult {
  filename: string;
  chatName: string;
  chatId: string;
  status: 'success' | 'error';
  message: string;
}

export default function ImportChatPage() {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const [isImporting, setIsImporting] = useState(false);
  const [importStatus, setImportStatus] = useState<string>('');
  const [importResults, setImportResults] = useState<ImportResult[]>([]);
  const [username, setUsername] = useState<string>('');
  const [selectedFiles, setSelectedFiles] = useState<FileList | null>(null);

  // Função para buscar username do usuário
  const getUsernameFromFirestore = async (): Promise<string> => {
    if (!user?.email) return 'unknown';

    try {
      const usuariosRef = collection(db, 'usuarios');
      const q = query(usuariosRef, where('email', '==', user.email));
      const querySnapshot = await getDocs(q);

      if (!querySnapshot.empty) {
        const userDoc = querySnapshot.docs[0];
        const userData = userDoc.data();
        return userData.username || user.email.split('@')[0];
      }

      return user.email.split('@')[0]; // fallback
    } catch (error) {
      console.error('Erro ao buscar username:', error);
      return user.email.split('@')[0]; // fallback
    }
  };

  // Carregar username quando o usuário estiver disponível
  useEffect(() => {
    const loadUsername = async () => {
      if (user?.email) {
        const fetchedUsername = await getUsernameFromFirestore();
        setUsername(fetchedUsername);
      }
    };
    loadUsername();
  }, [user?.email]);

  // Redirecionar se não estiver autenticado
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/login');
    }
  }, [user, authLoading, router]);

  const generateChatId = () => {
    return Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
  };

  const handleFileSelection = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      setSelectedFiles(files);
      setImportResults([]);
      setImportStatus(`📁 ${files.length} arquivo(s) selecionado(s). Clique em "Importar" para continuar.`);
    }
  };

  const importSingleChat = async (file: File): Promise<ImportResult> => {
    try {
      if (file.type !== 'application/json') {
        return {
          filename: file.name,
          chatName: '',
          chatId: '',
          status: 'error',
          message: 'Arquivo deve ser JSON'
        };
      }

      const fileContent = await file.text();
      const importedChat: ImportedChatData = JSON.parse(fileContent);

      // Gerar novo ID para evitar conflitos
      const newChatId = generateChatId();
      const now = new Date().toISOString();

      // Preparar dados para o Firestore (formato atual do sistema)
      const firestoreData = {
        context: importedChat.context || '',
        createdAt: now,
        folderId: null,
        frequencyPenalty: importedChat.frequency_penalty || 1.0,
        isFixed: false,
        lastUpdatedAt: now,
        lastUsedModel: importedChat.lastUsedModel || '',
        latexInstructions: importedChat.latexInstructions || false,
        maxTokens: importedChat.maxTokens || 2048,
        name: importedChat.name || 'Chat Importado',
        password: '',
        repetitionPenalty: importedChat.repetition_penalty || 1.0,
        sessionTime: {
          lastSessionStart: null,
          lastUpdated: null,
          totalTime: 0
        },
        systemPrompt: importedChat.system_prompt || '',
        temperature: importedChat.temperature || 1.0,
        ultimaMensagem: importedChat.messages.length > 0
          ? importedChat.messages[importedChat.messages.length - 1].content.substring(0, 100) + '...'
          : 'Chat importado',
        ultimaMensagemEm: importedChat.messages.length > 0
          ? new Date(importedChat.messages[importedChat.messages.length - 1].timestamp).toISOString()
          : now,
        updatedAt: now
      };

      // Criar documento no Firestore
      await setDoc(doc(db, 'usuarios', username, 'conversas', newChatId), firestoreData);

      // Preparar dados do chat.json para o Storage (formato atual do sistema)
      const chatJsonData = {
        id: newChatId,
        name: importedChat.name || 'Chat Importado',
        messages: importedChat.messages.map(msg => ({
          id: msg.id,
          content: msg.content,
          role: msg.role,
          timestamp: msg.timestamp,
          isFavorite: false,
          attachments: [],
          ...(msg.usage && { usage: msg.usage }),
          ...(msg.responseTime && { responseTime: msg.responseTime })
        })),
        createdAt: now,
        lastUpdated: now
      };

      // Salvar no Firebase Storage
      const chatJsonBlob = new Blob([JSON.stringify(chatJsonData, null, 2)], {
        type: 'application/json'
      });

      const storageRef = ref(storage, `usuarios/${username}/conversas/${newChatId}/chat.json`);
      await uploadBytes(storageRef, chatJsonBlob);

      return {
        filename: file.name,
        chatName: importedChat.name || 'Chat Importado',
        chatId: newChatId,
        status: 'success',
        message: 'Importado com sucesso'
      };

    } catch (error) {
      console.error('Erro ao importar chat:', error);
      return {
        filename: file.name,
        chatName: '',
        chatId: '',
        status: 'error',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  };

  const handleImportChats = async () => {
    if (!selectedFiles || selectedFiles.length === 0 || !username) return;

    setIsImporting(true);
    setImportResults([]);
    setImportStatus('🔄 Iniciando importação...');

    const results: ImportResult[] = [];
    const totalFiles = selectedFiles.length;

    for (let i = 0; i < totalFiles; i++) {
      const file = selectedFiles[i];
      setImportStatus(`🔄 Importando ${i + 1}/${totalFiles}: ${file.name}`);

      const result = await importSingleChat(file);
      results.push(result);
      setImportResults([...results]);
    }

    const successCount = results.filter(r => r.status === 'success').length;
    const errorCount = results.filter(r => r.status === 'error').length;

    setImportStatus(`✅ Importação concluída! ${successCount} sucesso(s), ${errorCount} erro(s)`);
    setIsImporting(false);
    setSelectedFiles(null);

    // Limpar o input
    const fileInput = document.getElementById('chat-files') as HTMLInputElement;
    if (fileInput) fileInput.value = '';
  };

  if (authLoading) {
    return (
      <div className="min-h-screen bg-gradient-rafthor flex items-center justify-center">
        <div className="text-white text-xl">Carregando...</div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-rafthor">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-white mb-4">
              Importar Chat do Rafthor Anterior
            </h1>
            <p className="text-white/80 text-lg">
              Faça upload do arquivo chat.json para importar suas conversas
            </p>
            <p className="text-white/60 text-sm mt-2">
              Usuário: {username}
            </p>
          </div>

          {/* Upload Area */}
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
            <div className="text-center">
              <div className="mb-6">
                <svg className="mx-auto h-16 w-16 text-white/60" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
              </div>

              <label htmlFor="chat-files" className="cursor-pointer">
                <div className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors duration-200 inline-block">
                  {isImporting ? 'Importando...' : 'Selecionar arquivos chat.json'}
                </div>
              </label>

              <input
                id="chat-files"
                type="file"
                accept=".json"
                multiple
                onChange={handleFileSelection}
                disabled={isImporting}
                className="hidden"
              />

              <p className="text-white/60 text-sm mt-4">
                Selecione um ou múltiplos arquivos .json exportados do Rafthor anterior
              </p>

              {/* Import Button */}
              {selectedFiles && selectedFiles.length > 0 && (
                <div className="mt-4">
                  <button
                    onClick={handleImportChats}
                    disabled={isImporting}
                    className="bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white px-6 py-3 rounded-lg transition-colors duration-200"
                  >
                    {isImporting ? 'Importando...' : `Importar ${selectedFiles.length} arquivo(s)`}
                  </button>
                </div>
              )}
            </div>

            {/* Status */}
            {importStatus && (
              <div className="mt-6 p-4 bg-white/5 rounded-lg border border-white/10">
                <p className="text-white text-center">{importStatus}</p>
              </div>
            )}

            {/* Results */}
            {importResults.length > 0 && (
              <div className="mt-6 space-y-2">
                <h4 className="text-white font-semibold mb-3">📊 Resultados da Importação:</h4>
                <div className="max-h-60 overflow-y-auto space-y-2">
                  {importResults.map((result, index) => (
                    <div
                      key={index}
                      className={`p-3 rounded-lg border ${
                        result.status === 'success'
                          ? 'bg-green-500/10 border-green-500/30 text-green-300'
                          : 'bg-red-500/10 border-red-500/30 text-red-300'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <p className="font-medium">{result.filename}</p>
                          {result.chatName && (
                            <p className="text-sm opacity-80">Chat: {result.chatName}</p>
                          )}
                          <p className="text-sm opacity-80">{result.message}</p>
                        </div>
                        <div className="ml-4">
                          {result.status === 'success' ? '✅' : '❌'}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Instructions */}
          <div className="mt-8 bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10">
            <h3 className="text-white font-semibold mb-3">📋 Instruções:</h3>
            <ul className="text-white/80 space-y-2 text-sm">
              <li>• Selecione um ou múltiplos arquivos chat.json do seu Rafthor anterior</li>
              <li>• Você pode selecionar vários arquivos de uma vez usando Ctrl+clique</li>
              <li>• Cada chat será importado com um novo ID para evitar conflitos</li>
              <li>• Todas as mensagens e configurações serão preservadas</li>
              <li>• Após a importação, você pode acessar os chats no dashboard</li>
              <li>• O processo mostrará o resultado de cada arquivo individualmente</li>
            </ul>
          </div>

          {/* Back to Dashboard */}
          <div className="text-center mt-8">
            <button
              onClick={() => router.push('/dashboard')}
              className="bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-lg transition-all duration-200 backdrop-blur-sm border border-white/30"
            >
              ← Voltar ao Dashboard
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
