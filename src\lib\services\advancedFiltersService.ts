import { AIModel } from '@/lib/types/chat';

export interface SmartCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  filter: (model: AIModel) => boolean;
}

class AdvancedFiltersService {
  private smartCategories: SmartCategory[] = [
    {
      id: 'vision',
      name: 'Vis<PERSON>',
      description: 'Modelos que processam imagens',
      icon: '👁️',
      filter: (model: AIModel) => {
        return !!(model.architecture?.input_modalities?.includes('image') ||
               model.name.toLowerCase().includes('vision') ||
               model.description?.toLowerCase().includes('vision') ||
               model.description?.toLowerCase().includes('image'));
      }
    },
    {
      id: 'coding',
      name: '<PERSON><PERSON><PERSON>',
      description: 'Modelos especializados em programação',
      icon: '💻',
      filter: (model: AIModel) => {
        const keywords = ['code', 'coding', 'programming', 'developer', 'coder'];
        return keywords.some(keyword =>
          model.name.toLowerCase().includes(keyword) ||
          !!(model.description?.toLowerCase().includes(keyword))
        );
      }
    },
    {
      id: 'reasoning',
      name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      description: 'Modelos otimizados para raciocínio lógico',
      icon: '🧠',
      filter: (model: AIModel) => {
        const keywords = ['reasoning', 'logic', 'math', 'analysis', 'thinking'];
        return keywords.some(keyword =>
          model.name.toLowerCase().includes(keyword) ||
          !!(model.description?.toLowerCase().includes(keyword))
        );
      }
    },
    {
      id: 'creative',
      name: 'Criativo',
      description: 'Modelos para tarefas criativas',
      icon: '🎨',
      filter: (model: AIModel) => {
        const keywords = ['creative', 'writing', 'story', 'art', 'creative'];
        return keywords.some(keyword =>
          model.name.toLowerCase().includes(keyword) ||
          !!(model.description?.toLowerCase().includes(keyword))
        );
      }
    },
    {
      id: 'fast',
      name: 'Rápido',
      description: 'Modelos otimizados para velocidade',
      icon: '⚡',
      filter: (model: AIModel) => {
        const keywords = ['fast', 'quick', 'speed', 'turbo', 'instant'];
        return keywords.some(keyword =>
          model.name.toLowerCase().includes(keyword) ||
          !!(model.description?.toLowerCase().includes(keyword))
        );
      }
    },
    {
      id: 'large_context',
      name: 'Grande Contexto',
      description: 'Modelos com contexto extenso (>32K)',
      icon: '📚',
      filter: (model: AIModel) => {
        return model.context_length > 32000;
      }
    },
    {
      id: 'cheap',
      name: 'Econômico',
      description: 'Modelos com preços baixos',
      icon: '💰',
      filter: (model: AIModel) => {
        const promptPrice = parseFloat(model.pricing.prompt);
        const completionPrice = parseFloat(model.pricing.completion);
        const totalPrice = promptPrice + completionPrice;
        return totalPrice < 0.000001; // Menos de $1 por 1M tokens
      }
    },
    {
      id: 'premium',
      name: 'Premium',
      description: 'Modelos de alta qualidade',
      icon: '⭐',
      filter: (model: AIModel) => {
        const keywords = ['gpt-4', 'claude-3', 'premium', 'pro', 'advanced'];
        return keywords.some(keyword => 
          model.name.toLowerCase().includes(keyword) ||
          model.id.toLowerCase().includes(keyword)
        );
      }
    }
  ];

  getSmartCategories(): SmartCategory[] {
    return this.smartCategories;
  }

  getModelsByCategory(models: AIModel[], categoryId: string): AIModel[] {
    const category = this.smartCategories.find(cat => cat.id === categoryId);
    if (!category) {
      return models;
    }

    return models.filter(category.filter);
  }

  getCategoryStats(models: AIModel[]): Array<{
    category: SmartCategory;
    count: number;
    percentage: number;
  }> {
    const totalModels = models.length;
    
    return this.smartCategories.map(category => {
      const matchingModels = models.filter(category.filter);
      return {
        category,
        count: matchingModels.length,
        percentage: totalModels > 0 ? (matchingModels.length / totalModels) * 100 : 0
      };
    });
  }

  // Filtros avançados por preço
  filterByPriceRange(models: AIModel[], minPrice: number, maxPrice: number): AIModel[] {
    return models.filter(model => {
      const promptPrice = parseFloat(model.pricing.prompt);
      const completionPrice = parseFloat(model.pricing.completion);
      const totalPrice = promptPrice + completionPrice;
      return totalPrice >= minPrice && totalPrice <= maxPrice;
    });
  }

  // Filtros por contexto
  filterByContextRange(models: AIModel[], minContext: number, maxContext: number): AIModel[] {
    return models.filter(model => {
      return model.context_length >= minContext && model.context_length <= maxContext;
    });
  }

  // Filtros por modalidades
  filterByInputModalities(models: AIModel[], modalities: string[]): AIModel[] {
    return models.filter(model => {
      if (!model.architecture?.input_modalities) return false;
      return modalities.every(modality => 
        model.architecture!.input_modalities.includes(modality)
      );
    });
  }

  // Busca por tags/palavras-chave
  filterByKeywords(models: AIModel[], keywords: string[]): AIModel[] {
    return models.filter(model => {
      const searchText = `${model.name} ${model.description || ''} ${model.id}`.toLowerCase();
      return keywords.some(keyword => 
        searchText.includes(keyword.toLowerCase())
      );
    });
  }

  // Filtros combinados
  applyAdvancedFilters(models: AIModel[], filters: {
    categories?: string[];
    priceRange?: { min: number; max: number };
    contextRange?: { min: number; max: number };
    inputModalities?: string[];
    keywords?: string[];
    onlyFavorites?: boolean;
  }): AIModel[] {
    let filteredModels = [...models];

    // Filtrar por categorias
    if (filters.categories && filters.categories.length > 0) {
      filteredModels = filteredModels.filter(model => {
        return filters.categories!.some(categoryId => {
          const category = this.smartCategories.find(cat => cat.id === categoryId);
          return category ? category.filter(model) : false;
        });
      });
    }

    // Filtrar por faixa de preço
    if (filters.priceRange) {
      filteredModels = this.filterByPriceRange(
        filteredModels, 
        filters.priceRange.min, 
        filters.priceRange.max
      );
    }

    // Filtrar por faixa de contexto
    if (filters.contextRange) {
      filteredModels = this.filterByContextRange(
        filteredModels,
        filters.contextRange.min,
        filters.contextRange.max
      );
    }

    // Filtrar por modalidades de entrada
    if (filters.inputModalities && filters.inputModalities.length > 0) {
      filteredModels = this.filterByInputModalities(filteredModels, filters.inputModalities);
    }

    // Filtrar por palavras-chave
    if (filters.keywords && filters.keywords.length > 0) {
      filteredModels = this.filterByKeywords(filteredModels, filters.keywords);
    }

    // Filtrar apenas favoritos
    if (filters.onlyFavorites) {
      filteredModels = filteredModels.filter(model => model.isFavorite);
    }

    return filteredModels;
  }

  // Sugestões de filtros baseadas nos modelos disponíveis
  getSuggestedFilters(models: AIModel[]): {
    popularCategories: Array<{ category: SmartCategory; count: number }>;
    priceRanges: Array<{ label: string; min: number; max: number; count: number }>;
    contextRanges: Array<{ label: string; min: number; max: number; count: number }>;
  } {
    const categoryStats = this.getCategoryStats(models);
    const popularCategories = categoryStats
      .filter(stat => stat.count > 0)
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    // Faixas de preço sugeridas (valores por token, não por 1M)
    const priceRanges = [
      { label: 'Gratuito', min: 0, max: 0 },
      { label: 'Muito Barato (< $1/1M)', min: 0.000000001, max: 0.000001 },
      { label: 'Barato ($1-10/1M)', min: 0.000001, max: 0.00001 },
      { label: 'Médio ($10-100/1M)', min: 0.00001, max: 0.0001 },
      { label: 'Caro (> $100/1M)', min: 0.0001, max: Infinity }
    ].map(range => ({
      ...range,
      count: this.filterByPriceRange(models, range.min, range.max).length
    }));

    // Faixas de contexto sugeridas
    const contextRanges = [
      { label: 'Pequeno (< 8K)', min: 0, max: 8000 },
      { label: 'Médio (8K - 32K)', min: 8000, max: 32000 },
      { label: 'Grande (32K - 128K)', min: 32000, max: 128000 },
      { label: 'Muito Grande (> 128K)', min: 128000, max: Infinity }
    ].map(range => ({
      ...range,
      count: this.filterByContextRange(models, range.min, range.max).length
    }));

    return {
      popularCategories,
      priceRanges,
      contextRanges
    };
  }
}

export const advancedFiltersService = new AdvancedFiltersService();
