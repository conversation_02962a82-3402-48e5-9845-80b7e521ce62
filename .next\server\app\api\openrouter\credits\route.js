"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/openrouter/credits/route";
exports.ids = ["app/api/openrouter/credits/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fopenrouter%2Fcredits%2Froute&page=%2Fapi%2Fopenrouter%2Fcredits%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fopenrouter%2Fcredits%2Froute.ts&appDir=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fopenrouter%2Fcredits%2Froute&page=%2Fapi%2Fopenrouter%2Fcredits%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fopenrouter%2Fcredits%2Froute.ts&appDir=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/node-polyfill-headers */ \"(rsc)/./node_modules/next/dist/server/node-polyfill-headers.js\");\n/* harmony import */ var next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var C_Users_Henri_Desktop_SiteRafthor_rafthor_src_app_api_openrouter_credits_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/openrouter/credits/route.ts */ \"(rsc)/./src/app/api/openrouter/credits/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_2__.RouteKind.APP_ROUTE,\n        page: \"/api/openrouter/credits/route\",\n        pathname: \"/api/openrouter/credits\",\n        filename: \"route\",\n        bundlePath: \"app/api/openrouter/credits/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\api\\\\openrouter\\\\credits\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Henri_Desktop_SiteRafthor_rafthor_src_app_api_openrouter_credits_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/openrouter/credits/route\";\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fopenrouter%2Fcredits%2Froute&page=%2Fapi%2Fopenrouter%2Fcredits%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fopenrouter%2Fcredits%2Froute.ts&appDir=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/openrouter/credits/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/openrouter/credits/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n\nasync function POST(request) {\n    try {\n        const { apiKey } = await request.json();\n        console.log(\"API Route - Recebida requisi\\xe7\\xe3o para buscar cr\\xe9ditos\");\n        console.log(\"API Key recebida:\", apiKey ? `${apiKey.substring(0, 10)}...` : \"N\\xe3o fornecida\");\n        if (!apiKey) {\n            console.log(\"API Route - Erro: API key n\\xe3o fornecida\");\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"API key \\xe9 obrigat\\xf3ria\"\n            }, {\n                status: 400\n            });\n        }\n        console.log(\"API Route - Fazendo requisi\\xe7\\xe3o para OpenRouter...\");\n        // Fazer requisição para a API da OpenRouter\n        const response = await fetch(\"https://openrouter.ai/api/v1/credits\", {\n            method: \"GET\",\n            headers: {\n                \"Authorization\": `Bearer ${apiKey}`,\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        console.log(\"API Route - Status da resposta:\", response.status);\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.log(\"API Route - Erro da OpenRouter:\", errorText);\n            throw new Error(`Erro da API OpenRouter: ${response.status} ${response.statusText}`);\n        }\n        const data = await response.json();\n        console.log(\"API Route - Dados recebidos da OpenRouter:\", data);\n        const { total_credits, total_usage } = data.data;\n        const balance = total_credits - total_usage;\n        console.log(\"API Route - Cr\\xe9ditos calculados:\", {\n            total_credits,\n            total_usage,\n            balance\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            balance,\n            total_credits,\n            total_usage\n        }, {\n            headers: {\n                \"Access-Control-Allow-Origin\": \"*\",\n                \"Access-Control-Allow-Methods\": \"POST, OPTIONS\",\n                \"Access-Control-Allow-Headers\": \"Content-Type, Authorization\"\n            }\n        });\n    } catch (error) {\n        console.error(\"Erro ao buscar cr\\xe9ditos da OpenRouter:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            error: \"Erro ao buscar cr\\xe9ditos da OpenRouter\",\n            details: error instanceof Error ? error.message : \"Erro desconhecido\"\n        }, {\n            status: 500,\n            headers: {\n                \"Access-Control-Allow-Origin\": \"*\",\n                \"Access-Control-Allow-Methods\": \"POST, OPTIONS\",\n                \"Access-Control-Allow-Headers\": \"Content-Type, Authorization\"\n            }\n        });\n    }\n}\n// OPTIONS - Para CORS\nasync function OPTIONS() {\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({}, {\n        headers: {\n            \"Access-Control-Allow-Origin\": \"*\",\n            \"Access-Control-Allow-Methods\": \"POST, OPTIONS\",\n            \"Access-Control-Allow-Headers\": \"Content-Type, Authorization\"\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/openrouter/credits/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fopenrouter%2Fcredits%2Froute&page=%2Fapi%2Fopenrouter%2Fcredits%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fopenrouter%2Fcredits%2Froute.ts&appDir=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();