'use client';

import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '@/contexts/AuthContext';
import { doc, updateDoc, setDoc, getDoc, deleteDoc, collection, getDocs, query, orderBy } from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL, listAll, deleteObject } from 'firebase/storage';
import { updatePassword, reauthenticateWithCredential, EmailAuthProvider } from 'firebase/auth';
import { db, storage } from '@/lib/firebase';

interface UserData {
  username: string;
  email: string;
  balance: number;
  createdAt: string;
  profileImage?: string;
}

interface SettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
  userData: UserData;
  onUserDataUpdate: (userData: UserData) => void;
}

interface AppearanceSettings {
  fonte: string;
  tamanhoFonte: number;
  palavrasPorSessao: number;
}

interface AIEndpoint {
  nome: string;
  url: string;
  apiKey: string;
  modeloPadrao: string;
  ativo: boolean;
}

interface Memory {
  titulo: string;
  conteudo: string;
  cor: string;
  categoria: string | null;
  chatId: string | null;
  global: boolean;
}

interface Chat {
  id: string;
  name: string;
  lastMessage: string;
  lastMessageTime: string;
}

interface MemoryCategory {
  nome: string;
  descricao: string;
  cor: string;
}

type TabType = 'geral' | 'aparencia' | 'ia' | 'memoria';

export default function SettingsModal({
  isOpen,
  onClose,
  userData,
  onUserDataUpdate
}: SettingsModalProps) {
  const { logout, user } = useAuth();
  const [activeTab, setActiveTab] = useState<TabType>('geral');
  const [loading, setLoading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Estados para cada aba
  const [generalData, setGeneralData] = useState({
    username: userData.username,
    profileImage: userData.profileImage || '',
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const [appearanceSettings, setAppearanceSettings] = useState<AppearanceSettings>({
    fonte: 'Inter',
    tamanhoFonte: 14,
    palavrasPorSessao: 5000
  });

  const [aiEndpoints, setAiEndpoints] = useState<AIEndpoint[]>([
    {
      nome: 'OpenRouter',
      url: 'https://openrouter.ai/api/v1/chat/completions',
      apiKey: '',
      modeloPadrao: 'meta-llama/llama-3.1-8b-instruct:free',
      ativo: false
    },
    {
      nome: 'DeepSeek',
      url: 'https://api.deepseek.com/v1/chat/completions',
      apiKey: '',
      modeloPadrao: 'deepseek-chat',
      ativo: false
    }
  ]);
  const [memories, setMemories] = useState<Memory[]>([]);
  const [memoryCategories, setMemoryCategories] = useState<MemoryCategory[]>([]);
  const [chats, setChats] = useState<Chat[]>([]);
  const [showAddMemory, setShowAddMemory] = useState(false);
  const [showAddCategory, setShowAddCategory] = useState(false);
  const [newMemory, setNewMemory] = useState<Memory>({
    titulo: '',
    conteudo: '',
    cor: '#3B82F6',
    categoria: null,
    chatId: null,
    global: true
  });
  const [newCategory, setNewCategory] = useState<MemoryCategory>({
    nome: '',
    descricao: '',
    cor: '#3B82F6'
  });
  const [showAddEndpoint, setShowAddEndpoint] = useState(false);
  const [newEndpoint, setNewEndpoint] = useState<AIEndpoint>({
    nome: '',
    url: '',
    apiKey: '',
    modeloPadrao: '',
    ativo: false
  });
  const [editingEndpoint, setEditingEndpoint] = useState<number | null>(null);
  const [editEndpointData, setEditEndpointData] = useState<AIEndpoint>({
    nome: '',
    url: '',
    apiKey: '',
    modeloPadrao: '',
    ativo: false
  });

  // Carregar configurações do Firestore
  useEffect(() => {
    const loadConfigurations = async () => {
      if (!userData.username) return;

      try {
        console.log('Carregando configurações para:', userData.username);
        const configDoc = await getDoc(doc(db, 'usuarios', userData.username, 'configuracoes', 'settings'));

        if (configDoc.exists()) {
          const config = configDoc.data();
          console.log('Configurações carregadas:', config);

          if (config.aparencia) {
            setAppearanceSettings(config.aparencia);
          }

          if (config.endpoints) {
            const endpointsArray = Object.values(config.endpoints) as AIEndpoint[];
            setAiEndpoints(endpointsArray);
          } else {
            // Manter endpoints padrão se não houver configuração salva
            setAiEndpoints([
              {
                nome: 'OpenRouter',
                url: 'https://openrouter.ai/api/v1/chat/completions',
                apiKey: '',
                modeloPadrao: 'meta-llama/llama-3.1-8b-instruct:free',
                ativo: false
              },
              {
                nome: 'DeepSeek',
                url: 'https://api.deepseek.com/v1/chat/completions',
                apiKey: '',
                modeloPadrao: 'deepseek-chat',
                ativo: false
              }
            ]);
          }

          if (config.memorias) {
            const memoriasArray = Object.values(config.memorias) as Memory[];
            setMemories(memoriasArray);
          }

          if (config.categorias) {
            const categoriasArray = Object.values(config.categorias) as MemoryCategory[];
            setMemoryCategories(categoriasArray);
          }
        } else {
          console.log('Nenhuma configuração encontrada, usando padrões');
          // Configurações padrão se não existir documento
          setAppearanceSettings({
            fonte: 'Inter',
            tamanhoFonte: 14,
            palavrasPorSessao: 5000
          });
        }
      } catch (error) {
        console.error('Erro ao carregar configurações:', error);
      }
    };

    if (isOpen && userData.username) {
      // Reset do estado do formulário geral quando abrir o modal
      setGeneralData({
        username: userData.username,
        profileImage: userData.profileImage || '',
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });

      loadConfigurations();
      loadChats();
    }
  }, [isOpen, userData.username, userData.profileImage]);

  // Função para carregar chats do usuário
  const loadChats = async () => {
    if (!userData?.username) return;

    try {
      const chatsRef = collection(db, 'usuarios', userData.username, 'conversas');
      const chatsQuery = query(chatsRef, orderBy('lastUpdatedAt', 'desc'));
      const chatsSnapshot = await getDocs(chatsQuery);

      const loadedChats: Chat[] = [];
      chatsSnapshot.forEach((doc) => {
        const data = doc.data();
        loadedChats.push({
          id: doc.id,
          name: data.name || 'Conversa sem nome',
          lastMessage: data.ultimaMensagem || 'Nenhuma mensagem ainda',
          lastMessageTime: data.ultimaMensagemEm || data.createdAt,
        });
      });

      setChats(loadedChats);
    } catch (error) {
      console.error('Erro ao carregar chats:', error);
    }
  };

  // Função auxiliar para deletar todos os dados do Storage de um usuário
  const deleteUserStorageData = async (username: string) => {
    try {
      console.log('Iniciando exclusão de dados do Storage para:', username);

      // Deletar toda a pasta do usuário no Storage
      const userStorageRef = ref(storage, `usuarios/${username}`);
      const userStorageList = await listAll(userStorageRef);

      // Função recursiva para deletar pastas e arquivos
      const deleteRecursively = async (folderRef: any) => {
        const folderList = await listAll(folderRef);

        // Deletar todos os arquivos na pasta atual
        const fileDeletePromises = folderList.items.map(item => deleteObject(item));
        await Promise.all(fileDeletePromises);

        // Deletar recursivamente todas as subpastas
        const folderDeletePromises = folderList.prefixes.map(prefix => deleteRecursively(prefix));
        await Promise.all(folderDeletePromises);
      };

      await deleteRecursively(userStorageRef);
      console.log('Todos os dados do Storage deletados para:', username);

    } catch (error) {
      console.log('Erro ao deletar dados do Storage ou pasta não encontrada:', error);
    }
  };

  // Função auxiliar para deletar recursivamente todos os documentos de um usuário
  const deleteUserDocuments = async (username: string) => {
    try {
      console.log('Iniciando exclusão de documentos para:', username);

      // Deletar subcoleção de configurações
      try {
        const configDoc = doc(db, 'usuarios', username, 'configuracoes', 'settings');
        const configSnapshot = await getDoc(configDoc);
        if (configSnapshot.exists()) {
          await deleteDoc(configDoc);
          console.log('Configurações deletadas');
        }
      } catch (error) {
        console.log('Erro ao deletar configurações:', error);
      }

      // Deletar outras subcoleções se existirem (chats, histórico, etc.)
      try {
        const chatsCollection = collection(db, 'usuarios', username, 'chats');
        const chatsSnapshot = await getDocs(chatsCollection);
        const deletePromises = chatsSnapshot.docs.map(doc => deleteDoc(doc.ref));
        await Promise.all(deletePromises);
        console.log('Chats deletados');
      } catch (error) {
        console.log('Erro ao deletar chats:', error);
      }

      // Deletar documento principal do usuário
      const userDocRef = doc(db, 'usuarios', username);
      await deleteDoc(userDocRef);
      console.log('Documento principal do usuário deletado');

    } catch (error) {
      console.error('Erro ao deletar documentos do usuário:', error);
      throw error;
    }
  };

  // Atualizar username no documento principal
  const updateUsername = async (newUsername: string, showSuccessAlert: boolean = true) => {
    if (!userData.username || !newUsername || newUsername === userData.username) {
      if (showSuccessAlert) alert('Nome de usuário inválido ou igual ao atual.');
      return false;
    }

    if (newUsername.length < 3) {
      if (showSuccessAlert) alert('Nome de usuário deve ter pelo menos 3 caracteres.');
      return false;
    }

    let newUserCreated = false;

    try {
      console.log('Atualizando username de', userData.username, 'para', newUsername);

      // Verificar se o novo username já existe
      const newUserDoc = await getDoc(doc(db, 'usuarios', newUsername));
      if (newUserDoc.exists()) {
        if (showSuccessAlert) alert('Este nome de usuário já está em uso. Escolha outro.');
        return false;
      }

      // Buscar o documento atual pelo username antigo
      const oldUserDocRef = doc(db, 'usuarios', userData.username);
      const oldUserDoc = await getDoc(oldUserDocRef);

      if (!oldUserDoc.exists()) {
        if (showSuccessAlert) alert('Usuário não encontrado.');
        return false;
      }

      const currentData = oldUserDoc.data();

      // Criar novo documento com o novo username
      await setDoc(doc(db, 'usuarios', newUsername), {
        ...currentData,
        username: newUsername,
        updatedAt: new Date().toISOString()
      });

      newUserCreated = true;
      console.log('Novo documento criado para:', newUsername);

        // Copiar todas as configurações e subcoleções
        try {
          // Copiar configurações principais
          const configDoc = await getDoc(doc(db, 'usuarios', userData.username, 'configuracoes', 'settings'));
          if (configDoc.exists()) {
            await setDoc(doc(db, 'usuarios', newUsername, 'configuracoes', 'settings'), configDoc.data());
            console.log('Configurações copiadas para novo username');
          }

          // Copiar chats se existirem
          try {
            const chatsCollection = collection(db, 'usuarios', userData.username, 'chats');
            const chatsSnapshot = await getDocs(chatsCollection);

            for (const chatDoc of chatsSnapshot.docs) {
              const chatData = chatDoc.data();
              await setDoc(doc(db, 'usuarios', newUsername, 'chats', chatDoc.id), chatData);
            }

            if (chatsSnapshot.docs.length > 0) {
              console.log(`${chatsSnapshot.docs.length} chats copiados para novo username`);
            }
          } catch (chatsError) {
            console.log('Erro ao copiar chats:', chatsError);
          }

        } catch (configError) {
          console.log('Erro ao copiar dados:', configError);
        }

        // Deletar todos os documentos do usuário antigo
        await deleteUserDocuments(userData.username);
        console.log('Todos os documentos do usuário antigo foram deletados');

        // Atualizar estado local
        onUserDataUpdate({
          ...userData,
          username: newUsername
        });

        if (showSuccessAlert) alert('Nome de usuário atualizado com sucesso!');
        return true;

    } catch (error) {
      console.error('Erro ao atualizar username:', error);

      // Se houve erro e o novo usuário foi criado, tentar fazer rollback
      if (newUserCreated) {
        try {
          await deleteDoc(doc(db, 'usuarios', newUsername));
          console.log('Rollback realizado - novo usuário deletado');
        } catch (rollbackError) {
          console.error('Erro no rollback:', rollbackError);
        }
      }

      if (showSuccessAlert) alert(`Erro ao atualizar nome de usuário: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
      return false;
    }
  };

  // Salvar configurações no Firestore
  const saveConfigurations = async () => {
    if (!userData.username) {
      alert('Erro: usuário não identificado');
      return;
    }

    try {
      setLoading(true);

      // Verificar se o username foi alterado e atualizá-lo primeiro
      if (generalData.username !== userData.username) {
        const usernameUpdated = await updateUsername(generalData.username, false);
        if (!usernameUpdated) {
          // Se falhou ao atualizar o username, interromper o processo
          return;
        }
      }

      // Determinar qual username usar (o novo se foi alterado)
      const currentUsername = generalData.username !== userData.username ? generalData.username : userData.username;

      const configData = {
        aparencia: {
          fonte: appearanceSettings.fonte,
          tamanhoFonte: appearanceSettings.tamanhoFonte,
          palavrasPorSessao: appearanceSettings.palavrasPorSessao
        },
        endpoints: {} as Record<string, AIEndpoint>,
        memorias: {} as Record<string, Memory>,
        categorias: {} as Record<string, MemoryCategory>,
        updatedAt: new Date().toISOString()
      };

      // Converter arrays para objetos
      aiEndpoints.forEach((endpoint, index) => {
        configData.endpoints[endpoint.nome || `endpoint_${index}`] = endpoint;
      });

      memories.forEach((memory, index) => {
        configData.memorias[`memoria_${index}`] = memory;
      });

      memoryCategories.forEach((category, index) => {
        configData.categorias[category.nome || `categoria_${index}`] = category;
      });

      console.log('Salvando configurações para:', currentUsername);
      console.log('Dados a serem salvos:', configData);

      // Usar setDoc com merge para não sobrescrever outros dados
      const docRef = doc(db, 'usuarios', currentUsername, 'configuracoes', 'settings');
      await setDoc(docRef, configData);

      console.log('Configurações salvas com sucesso no Firestore');
      alert('Configurações salvas com sucesso!');

    } catch (error) {
      console.error('Erro ao salvar configurações:', error);
      alert(`Erro ao salvar configurações: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  // Funções utilitárias
  const handleProfileImageUpload = async (file: File) => {
    if (!user) return;

    try {
      setLoading(true);
      const imageRef = ref(storage, `usuarios/${userData.username}/profile.jpg`);
      await uploadBytes(imageRef, file);
      const downloadURL = await getDownloadURL(imageRef);

      setGeneralData(prev => ({ ...prev, profileImage: downloadURL }));

      // Atualizar no Firestore
      const userDocRef = doc(db, 'usuarios', userData.username);
      await updateDoc(userDocRef, { profileImage: downloadURL });

      onUserDataUpdate({ ...userData, profileImage: downloadURL });
      alert('Foto de perfil atualizada com sucesso!');
    } catch (error) {
      console.error('Erro ao fazer upload da imagem:', error);
      alert('Erro ao atualizar foto de perfil.');
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordChange = async () => {
    if (!user || !generalData.currentPassword || !generalData.newPassword) {
      alert('Preencha todos os campos de senha.');
      return;
    }

    if (generalData.newPassword !== generalData.confirmPassword) {
      alert('As senhas não coincidem.');
      return;
    }

    try {
      setLoading(true);
      const credential = EmailAuthProvider.credential(user.email!, generalData.currentPassword);
      await reauthenticateWithCredential(user, credential);
      await updatePassword(user, generalData.newPassword);

      setGeneralData(prev => ({
        ...prev,
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      }));

      alert('Senha alterada com sucesso!');
    } catch (error) {
      console.error('Erro ao alterar senha:', error);
      alert('Erro ao alterar senha. Verifique a senha atual.');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    if (confirm('Tem certeza que deseja sair?')) {
      await logout();
      onClose();
    }
  };

  // Funções para gerenciar endpoints de IA
  const handleAddEndpoint = () => {
    if (!newEndpoint.nome || !newEndpoint.url || !newEndpoint.apiKey) {
      alert('Preencha todos os campos obrigatórios.');
      return;
    }

    setAiEndpoints(prev => [...prev, { ...newEndpoint }]);
    setNewEndpoint({
      nome: '',
      url: '',
      apiKey: '',
      modeloPadrao: '',
      ativo: false
    });
    setShowAddEndpoint(false);
    alert('Endpoint adicionado com sucesso!');
  };

  const handleToggleEndpoint = (index: number) => {
    setAiEndpoints(prev => prev.map((endpoint, i) =>
      i === index ? { ...endpoint, ativo: !endpoint.ativo } : endpoint
    ));
  };

  const handleDeleteEndpoint = (index: number) => {
    if (confirm('Tem certeza que deseja deletar este endpoint?')) {
      setAiEndpoints(prev => prev.filter((_, i) => i !== index));
    }
  };

  const handleEditEndpoint = (index: number) => {
    const endpoint = aiEndpoints[index];
    setEditEndpointData({ ...endpoint });
    setEditingEndpoint(index);
  };

  const handleSaveEditEndpoint = () => {
    if (editingEndpoint === null) return;

    if (!editEndpointData.apiKey || !editEndpointData.modeloPadrao) {
      alert('API Key e Modelo Padrão são obrigatórios.');
      return;
    }

    setAiEndpoints(prev => prev.map((endpoint, i) =>
      i === editingEndpoint ? { ...editEndpointData } : endpoint
    ));

    setEditingEndpoint(null);
    setEditEndpointData({
      nome: '',
      url: '',
      apiKey: '',
      modeloPadrao: '',
      ativo: false
    });

    alert('Endpoint atualizado com sucesso!');
  };

  const handleCancelEditEndpoint = () => {
    setEditingEndpoint(null);
    setEditEndpointData({
      nome: '',
      url: '',
      apiKey: '',
      modeloPadrao: '',
      ativo: false
    });
  };

  const handleTestEndpoint = async (endpoint: AIEndpoint) => {
    if (!endpoint.apiKey) {
      alert('API Key é necessária para testar o endpoint.');
      return;
    }

    try {
      setLoading(true);
      const response = await fetch(endpoint.url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${endpoint.apiKey}`
        },
        body: JSON.stringify({
          model: endpoint.modeloPadrao || 'gpt-3.5-turbo',
          messages: [{ role: 'user', content: 'Test message' }],
          max_tokens: 10
        })
      });

      if (response.ok) {
        alert('✅ Endpoint testado com sucesso!');
      } else {
        alert('❌ Erro ao testar endpoint. Verifique as configurações.');
      }
    } catch (error) {
      console.error('Erro ao testar endpoint:', error);
      alert('❌ Erro ao conectar com o endpoint.');
    } finally {
      setLoading(false);
    }
  };

  // Funções para gerenciar memórias
  const handleAddCategory = () => {
    if (!newCategory.nome) {
      alert('Nome da categoria é obrigatório.');
      return;
    }

    setMemoryCategories(prev => [...prev, { ...newCategory }]);
    setNewCategory({
      nome: '',
      descricao: '',
      cor: '#3B82F6'
    });
    setShowAddCategory(false);
    alert('Categoria criada com sucesso!');
  };

  const handleAddMemory = () => {
    if (!newMemory.titulo || !newMemory.conteudo) {
      alert('Título e conteúdo são obrigatórios.');
      return;
    }

    setMemories(prev => [...prev, { ...newMemory }]);
    setNewMemory({
      titulo: '',
      conteudo: '',
      cor: '#3B82F6',
      categoria: null,
      chatId: null,
      global: true
    });
    setShowAddMemory(false);
    alert('Memória criada com sucesso!');
  };

  const handleDeleteMemory = (index: number) => {
    if (confirm('Tem certeza que deseja deletar esta memória?')) {
      setMemories(prev => prev.filter((_, i) => i !== index));
    }
  };

  const handleDeleteCategory = (index: number) => {
    if (confirm('Tem certeza que deseja deletar esta categoria?')) {
      setMemoryCategories(prev => prev.filter((_, i) => i !== index));
    }
  };

  const colors = [
    '#3B82F6', '#EF4444', '#10B981', '#F59E0B',
    '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16'
  ];

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        >
          <motion.div
            initial={{ scale: 0.95, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.95, opacity: 0 }}
            className="bg-gradient-to-br from-blue-900/95 to-blue-800/95 backdrop-blur-sm
                      border border-white/20 rounded-2xl w-full max-w-6xl max-h-[95vh] overflow-hidden
                      mx-4 lg:mx-0"
          >

            {/* Header */}
            <div className="flex items-center justify-between p-4 lg:p-6 border-b border-white/20">
              <div>
                <h2 className="text-xl lg:text-2xl font-bold text-white">Configurações</h2>
                <p className="text-white/60 text-sm lg:hidden mt-1">
                  {activeTab === 'geral' && 'Informações pessoais e senha'}
                  {activeTab === 'aparencia' && 'Personalização da interface'}
                  {activeTab === 'ia' && 'Endpoints de inteligência artificial'}
                  {activeTab === 'memoria' && 'Sistema de memórias'}
                </p>
              </div>
              <button
                onClick={onClose}
                className="text-white/60 hover:text-white transition-colors p-2 hover:bg-white/10 rounded-lg"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="flex flex-col lg:flex-row">
              {/* Sidebar de navegação */}
              <div className="w-full lg:w-64 bg-white/5 border-b lg:border-b-0 lg:border-r border-white/10">
                <nav className="p-2 lg:p-4 space-y-1 lg:space-y-2 overflow-x-auto lg:overflow-x-visible">
                  <div className="flex lg:flex-col space-x-2 lg:space-x-0 lg:space-y-2 min-w-max lg:min-w-0">
                  <button
                    onClick={() => setActiveTab('geral')}
                    className={`w-full lg:w-auto text-left px-3 lg:px-4 py-2 lg:py-3 rounded-lg transition-all duration-200 flex items-center space-x-2 lg:space-x-3 whitespace-nowrap ${
                      activeTab === 'geral'
                        ? 'bg-blue-600 text-white shadow-lg'
                        : 'text-white/70 hover:text-white hover:bg-white/10'
                    }`}
                  >
                    <svg className="w-4 lg:w-5 h-4 lg:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    <span className="font-medium text-sm lg:text-base">Geral</span>
                  </button>

                  <button
                    onClick={() => setActiveTab('aparencia')}
                    className={`w-full lg:w-auto text-left px-3 lg:px-4 py-2 lg:py-3 rounded-lg transition-all duration-200 flex items-center space-x-2 lg:space-x-3 whitespace-nowrap ${
                      activeTab === 'aparencia'
                        ? 'bg-blue-600 text-white shadow-lg'
                        : 'text-white/70 hover:text-white hover:bg-white/10'
                    }`}
                  >
                    <svg className="w-4 lg:w-5 h-4 lg:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                        d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z" />
                    </svg>
                    <span className="font-medium text-sm lg:text-base">Aparência</span>
                  </button>

                  <button
                    onClick={() => setActiveTab('ia')}
                    className={`w-full lg:w-auto text-left px-3 lg:px-4 py-2 lg:py-3 rounded-lg transition-all duration-200 flex items-center space-x-2 lg:space-x-3 whitespace-nowrap ${
                      activeTab === 'ia'
                        ? 'bg-blue-600 text-white shadow-lg'
                        : 'text-white/70 hover:text-white hover:bg-white/10'
                    }`}
                  >
                    <svg className="w-4 lg:w-5 h-4 lg:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                        d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    <span className="font-medium text-sm lg:text-base">IA</span>
                  </button>

                  <button
                    onClick={() => setActiveTab('memoria')}
                    className={`w-full lg:w-auto text-left px-3 lg:px-4 py-2 lg:py-3 rounded-lg transition-all duration-200 flex items-center space-x-2 lg:space-x-3 whitespace-nowrap ${
                      activeTab === 'memoria'
                        ? 'bg-blue-600 text-white shadow-lg'
                        : 'text-white/70 hover:text-white hover:bg-white/10'
                    }`}
                  >
                    <svg className="w-4 lg:w-5 h-4 lg:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                        d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                    </svg>
                    <span className="font-medium text-sm lg:text-base">Memória</span>
                  </button>
                  </div>
                </nav>
              </div>

              {/* Conteúdo principal */}
              <div className="flex-1 p-4 lg:p-6 overflow-y-auto max-h-[calc(95vh-200px)]">
                <AnimatePresence mode="wait">
                  {activeTab === 'geral' && (
                    <motion.div
                      key="geral"
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -20 }}
                      className="space-y-8"
                    >
                      <div>
                        <h3 className="text-2xl font-bold text-white mb-6">Configurações Gerais</h3>

                        {/* Foto de Perfil */}
                        <div className="bg-white/5 border border-white/10 rounded-xl p-6 mb-6">
                          <h4 className="text-lg font-semibold text-white mb-4">Foto de Perfil</h4>
                          <div className="flex items-center space-x-6">
                            <div className="relative">
                              {generalData.profileImage ? (
                                <img
                                  src={generalData.profileImage}
                                  alt="Profile"
                                  className="w-20 h-20 rounded-full object-cover border-2 border-white/20"
                                />
                              ) : (
                                <div className="w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center">
                                  <span className="text-white font-bold text-2xl">
                                    {userData.username.charAt(0).toUpperCase()}
                                  </span>
                                </div>
                              )}
                            </div>
                            <div>
                              <button
                                onClick={() => fileInputRef.current?.click()}
                                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg
                                         transition-all duration-200 font-medium"
                              >
                                Alterar Foto
                              </button>
                              <p className="text-white/60 text-sm mt-2">
                                JPG, PNG ou GIF. Máximo 5MB.
                              </p>
                            </div>
                          </div>
                          <input
                            ref={fileInputRef}
                            type="file"
                            accept="image/*"
                            onChange={(e) => {
                              const file = e.target.files?.[0];
                              if (file) handleProfileImageUpload(file);
                            }}
                            className="hidden"
                          />
                        </div>

                        {/* Nome de usuário */}
                        <div className="bg-white/5 border border-white/10 rounded-xl p-6 mb-6">
                          <h4 className="text-lg font-semibold text-white mb-4">Nome de Usuário</h4>
                          <div className="space-y-4">
                            <input
                              type="text"
                              value={generalData.username}
                              onChange={(e) => setGeneralData(prev => ({ ...prev, username: e.target.value }))}
                              className="w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg
                                       px-4 py-3 text-white placeholder-white/50 focus:outline-none
                                       focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              placeholder="Digite seu nome de usuário"
                            />
                            {generalData.username !== userData.username && (
                              <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-3">
                                <p className="text-yellow-300 text-sm">
                                  ⚠️ Nome de usuário alterado. Clique em "Salvar Configurações" para aplicar as mudanças.
                                </p>
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Alterar Senha */}
                        <div className="bg-white/5 border border-white/10 rounded-xl p-6">
                          <h4 className="text-lg font-semibold text-white mb-4">Alterar Senha</h4>
                          <div className="space-y-4">
                            <div>
                              <label className="block text-white/80 text-sm font-medium mb-2">
                                Senha Atual
                              </label>
                              <input
                                type="password"
                                value={generalData.currentPassword}
                                onChange={(e) => setGeneralData(prev => ({ ...prev, currentPassword: e.target.value }))}
                                className="w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg
                                         px-4 py-3 text-white placeholder-white/50 focus:outline-none
                                         focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                placeholder="Digite sua senha atual"
                              />
                            </div>
                            <div>
                              <label className="block text-white/80 text-sm font-medium mb-2">
                                Nova Senha
                              </label>
                              <input
                                type="password"
                                value={generalData.newPassword}
                                onChange={(e) => setGeneralData(prev => ({ ...prev, newPassword: e.target.value }))}
                                className="w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg
                                         px-4 py-3 text-white placeholder-white/50 focus:outline-none
                                         focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                placeholder="Digite sua nova senha"
                              />
                            </div>
                            <div>
                              <label className="block text-white/80 text-sm font-medium mb-2">
                                Confirmar Nova Senha
                              </label>
                              <input
                                type="password"
                                value={generalData.confirmPassword}
                                onChange={(e) => setGeneralData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                                className="w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg
                                         px-4 py-3 text-white placeholder-white/50 focus:outline-none
                                         focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                placeholder="Confirme sua nova senha"
                              />
                            </div>
                            <button
                              onClick={handlePasswordChange}
                              disabled={loading}
                              className="bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed
                                       text-white px-6 py-2 rounded-lg transition-all duration-200 font-medium"
                            >
                              {loading ? 'Alterando...' : 'Alterar Senha'}
                            </button>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  )}

                  {activeTab === 'aparencia' && (
                    <motion.div
                      key="aparencia"
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -20 }}
                      className="space-y-8"
                    >
                      <div>
                        <h3 className="text-2xl font-bold text-white mb-6">Configurações de Aparência</h3>

                        {/* Fonte do Chat */}
                        <div className="bg-white/5 border border-white/10 rounded-xl p-6 mb-6">
                          <h4 className="text-lg font-semibold text-white mb-4">Fonte do Chat</h4>
                          <div className="space-y-4">
                            <div>
                              <label className="block text-white/80 text-sm font-medium mb-2">
                                Família da Fonte
                              </label>
                              <select
                                value={appearanceSettings.fonte}
                                onChange={(e) => setAppearanceSettings(prev => ({ ...prev, fonte: e.target.value }))}
                                className="w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg
                                         px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500
                                         focus:border-transparent"
                              >
                                <option value="Inter" className="bg-gray-800">Inter</option>
                                <option value="Roboto" className="bg-gray-800">Roboto</option>
                                <option value="JetBrains Mono" className="bg-gray-800">JetBrains Mono</option>
                                <option value="Lato" className="bg-gray-800">Lato</option>
                                <option value="Fira Code" className="bg-gray-800">Fira Code</option>
                                <option value="Merriweather" className="bg-gray-800">Merriweather</option>
                                <option value="Open Sans" className="bg-gray-800">Open Sans</option>
                                <option value="Source Sans Pro" className="bg-gray-800">Source Sans Pro</option>
                                <option value="Poppins" className="bg-gray-800">Poppins</option>
                                <option value="Nunito" className="bg-gray-800">Nunito</option>
                              </select>
                            </div>

                            <div className="bg-white/5 border border-white/10 rounded-lg p-4">
                              <p className="text-white/80 text-sm mb-2">Pré-visualização:</p>
                              <div
                                className="text-white p-3 bg-white/5 rounded border border-white/10"
                                style={{ fontFamily: appearanceSettings.fonte, fontSize: `${appearanceSettings.tamanhoFonte}px` }}
                              >
                                Esta é uma mensagem de exemplo para visualizar a fonte selecionada.
                                Lorem ipsum dolor sit amet, consectetur adipiscing elit.
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Tamanho da Fonte */}
                        <div className="bg-white/5 border border-white/10 rounded-xl p-6 mb-6">
                          <h4 className="text-lg font-semibold text-white mb-4">Tamanho da Fonte</h4>
                          <div className="space-y-4">
                            <div>
                              <label className="block text-white/80 text-sm font-medium mb-2">
                                Tamanho: {appearanceSettings.tamanhoFonte}px
                              </label>
                              <input
                                type="range"
                                min="10"
                                max="24"
                                value={appearanceSettings.tamanhoFonte}
                                onChange={(e) => setAppearanceSettings(prev => ({ ...prev, tamanhoFonte: parseInt(e.target.value) }))}
                                className="w-full h-2 bg-white/20 rounded-lg appearance-none cursor-pointer slider"
                              />
                              <div className="flex justify-between text-xs text-white/60 mt-1">
                                <span>10px</span>
                                <span>24px</span>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Sessões de Chat */}
                        <div className="bg-white/5 border border-white/10 rounded-xl p-6">
                          <h4 className="text-lg font-semibold text-white mb-4">Sessões de Chat</h4>
                          <div className="space-y-4">
                            <div className="flex items-center justify-between">
                              <div>
                                <h5 className="text-white font-medium">Divisão Automática</h5>
                                <p className="text-white/60 text-sm">
                                  Dividir chats longos em sessões baseadas na contagem de palavras
                                </p>
                              </div>
                              <button
                                className="bg-blue-600 relative inline-flex h-6 w-11 items-center rounded-full transition-colors"
                              >
                                <span className="translate-x-6 inline-block h-4 w-4 transform rounded-full bg-white transition" />
                              </button>
                            </div>

                            <div>
                              <label className="block text-white/80 text-sm font-medium mb-2">
                                Palavras por Sessão: {appearanceSettings.palavrasPorSessao.toLocaleString()}
                              </label>
                              <input
                                type="range"
                                min="1000"
                                max="20000"
                                step="500"
                                value={appearanceSettings.palavrasPorSessao}
                                onChange={(e) => setAppearanceSettings(prev => ({ ...prev, palavrasPorSessao: parseInt(e.target.value) }))}
                                className="w-full h-2 bg-white/20 rounded-lg appearance-none cursor-pointer slider"
                              />
                              <div className="flex justify-between text-xs text-white/60 mt-1">
                                <span>1.000</span>
                                <span>20.000</span>
                              </div>
                            </div>

                            <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3">
                              <p className="text-blue-300 text-sm">
                                💡 <strong>Dica:</strong> Sessões menores carregam mais rápido, mas podem fragmentar conversas longas.
                                Recomendamos entre 3.000-8.000 palavras para melhor experiência.
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  )}

                  {activeTab === 'ia' && (
                    <motion.div
                      key="ia"
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -20 }}
                      className="space-y-8"
                    >
                      <div>
                        <h3 className="text-2xl font-bold text-white mb-6">Inteligência Artificial</h3>

                        {/* Botão Adicionar Endpoint */}
                        <div className="flex justify-between items-center mb-6">
                          <p className="text-white/80">
                            Gerencie seus endpoints de IA personalizados
                          </p>
                          <button
                            onClick={() => setShowAddEndpoint(!showAddEndpoint)}
                            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg
                                     transition-all duration-200 font-medium flex items-center space-x-2"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                            </svg>
                            <span>Adicionar Endpoint</span>
                          </button>
                        </div>

                        {/* Formulário Adicionar Endpoint */}
                        {showAddEndpoint && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            exit={{ opacity: 0, height: 0 }}
                            className="bg-white/5 border border-white/10 rounded-xl p-6 mb-6"
                          >
                            <h4 className="text-lg font-semibold text-white mb-4">Novo Endpoint</h4>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div>
                                <label className="block text-white/80 text-sm font-medium mb-2">
                                  Nome do Endpoint *
                                </label>
                                <input
                                  type="text"
                                  value={newEndpoint.nome}
                                  onChange={(e) => setNewEndpoint(prev => ({ ...prev, nome: e.target.value }))}
                                  className="w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg
                                           px-4 py-3 text-white placeholder-white/50 focus:outline-none
                                           focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                  placeholder="Ex: Meu Endpoint"
                                />
                              </div>
                              <div>
                                <label className="block text-white/80 text-sm font-medium mb-2">
                                  URL do Endpoint *
                                </label>
                                <input
                                  type="url"
                                  value={newEndpoint.url}
                                  onChange={(e) => setNewEndpoint(prev => ({ ...prev, url: e.target.value }))}
                                  className="w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg
                                           px-4 py-3 text-white placeholder-white/50 focus:outline-none
                                           focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                  placeholder="https://api.exemplo.com/v1/chat/completions"
                                />
                              </div>
                              <div>
                                <label className="block text-white/80 text-sm font-medium mb-2">
                                  API Key *
                                </label>
                                <input
                                  type="password"
                                  value={newEndpoint.apiKey}
                                  onChange={(e) => setNewEndpoint(prev => ({ ...prev, apiKey: e.target.value }))}
                                  className="w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg
                                           px-4 py-3 text-white placeholder-white/50 focus:outline-none
                                           focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                  placeholder="sk-..."
                                />
                              </div>
                              <div>
                                <label className="block text-white/80 text-sm font-medium mb-2">
                                  Modelo Padrão
                                </label>
                                <input
                                  type="text"
                                  value={newEndpoint.modeloPadrao}
                                  onChange={(e) => setNewEndpoint(prev => ({ ...prev, modeloPadrao: e.target.value }))}
                                  className="w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg
                                           px-4 py-3 text-white placeholder-white/50 focus:outline-none
                                           focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                  placeholder="gpt-3.5-turbo"
                                />
                              </div>
                            </div>
                            <div className="flex justify-end space-x-3 mt-4">
                              <button
                                onClick={() => setShowAddEndpoint(false)}
                                className="px-4 py-2 text-white/70 hover:text-white transition-colors"
                              >
                                Cancelar
                              </button>
                              <button
                                onClick={handleAddEndpoint}
                                className="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg
                                         transition-all duration-200 font-medium"
                              >
                                Adicionar
                              </button>
                            </div>
                          </motion.div>
                        )}

                        {/* Lista de Endpoints */}
                        <div className="space-y-4">
                          {aiEndpoints.map((endpoint, index) => (
                            <div key={index} className="bg-white/5 border border-white/10 rounded-xl p-6">
                              <div className="flex items-center justify-between mb-4">
                                <div className="flex items-center space-x-3">
                                  <div className={`w-3 h-3 rounded-full ${endpoint.ativo ? 'bg-green-500' : 'bg-gray-500'}`} />
                                  <h4 className="text-lg font-semibold text-white">{endpoint.nome}</h4>
                                  {(endpoint.nome === 'OpenRouter' || endpoint.nome === 'DeepSeek') && (
                                    <span className="bg-blue-500/20 text-blue-300 text-xs px-2 py-1 rounded-full">
                                      Pré-configurado
                                    </span>
                                  )}
                                </div>
                                <div className="flex items-center space-x-2">
                                  <button
                                    onClick={() => handleToggleEndpoint(index)}
                                    className={`px-3 py-1 rounded-lg text-sm font-medium transition-all duration-200 ${
                                      endpoint.ativo
                                        ? 'bg-green-600 hover:bg-green-700 text-white'
                                        : 'bg-gray-600 hover:bg-gray-700 text-white'
                                    }`}
                                  >
                                    {endpoint.ativo ? 'Ativo' : 'Inativo'}
                                  </button>
                                  <button
                                    onClick={() => handleTestEndpoint(endpoint)}
                                    disabled={loading || !endpoint.apiKey}
                                    className="bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed
                                             text-white px-3 py-1 rounded-lg text-sm font-medium transition-all duration-200"
                                  >
                                    Testar
                                  </button>
                                  <button
                                    onClick={() => handleEditEndpoint(index)}
                                    className="bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded-lg
                                             text-sm font-medium transition-all duration-200"
                                  >
                                    Editar
                                  </button>
                                  {endpoint.nome !== 'OpenRouter' && endpoint.nome !== 'DeepSeek' && (
                                    <button
                                      onClick={() => handleDeleteEndpoint(index)}
                                      className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded-lg
                                               text-sm font-medium transition-all duration-200"
                                    >
                                      Deletar
                                    </button>
                                  )}
                                </div>
                              </div>

                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                <div>
                                  <span className="text-white/60">URL:</span>
                                  <p className="text-white font-mono text-xs break-all">{endpoint.url}</p>
                                </div>
                                <div>
                                  <span className="text-white/60">Modelo:</span>
                                  <p className="text-white">{endpoint.modeloPadrao || 'Não especificado'}</p>
                                </div>
                                <div>
                                  <span className="text-white/60">API Key:</span>
                                  <p className="text-white font-mono text-xs">
                                    {endpoint.apiKey ? '••••••••••••' + endpoint.apiKey.slice(-4) : 'Não configurada'}
                                  </p>
                                </div>
                                <div>
                                  <span className="text-white/60">Status:</span>
                                  <p className={`font-medium ${endpoint.ativo ? 'text-green-400' : 'text-gray-400'}`}>
                                    {endpoint.ativo ? 'Ativo' : 'Inativo'}
                                  </p>
                                </div>
                              </div>

                              {/* Modal de Edição */}
                              {editingEndpoint === index && (
                                <motion.div
                                  initial={{ opacity: 0, height: 0 }}
                                  animate={{ opacity: 1, height: 'auto' }}
                                  exit={{ opacity: 0, height: 0 }}
                                  className="mt-4 pt-4 border-t border-white/10"
                                >
                                  <h5 className="text-white font-semibold mb-4">Editar Endpoint</h5>
                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                      <label className="block text-white/80 text-sm font-medium mb-2">
                                        API Key *
                                      </label>
                                      <input
                                        type="password"
                                        value={editEndpointData.apiKey}
                                        onChange={(e) => setEditEndpointData(prev => ({ ...prev, apiKey: e.target.value }))}
                                        className="w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg
                                                 px-4 py-2 text-white placeholder-white/50 focus:outline-none
                                                 focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                                        placeholder="Cole sua API Key aqui..."
                                      />
                                    </div>
                                    <div>
                                      <label className="block text-white/80 text-sm font-medium mb-2">
                                        Modelo Padrão *
                                      </label>
                                      <input
                                        type="text"
                                        value={editEndpointData.modeloPadrao}
                                        onChange={(e) => setEditEndpointData(prev => ({ ...prev, modeloPadrao: e.target.value }))}
                                        className="w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg
                                                 px-4 py-2 text-white placeholder-white/50 focus:outline-none
                                                 focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                                        placeholder="Ex: gpt-4, claude-3-sonnet, etc."
                                      />
                                    </div>
                                  </div>
                                  <div className="flex justify-end space-x-2 mt-4">
                                    <button
                                      onClick={handleCancelEditEndpoint}
                                      className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg
                                               text-sm font-medium transition-all duration-200"
                                    >
                                      Cancelar
                                    </button>
                                    <button
                                      onClick={handleSaveEditEndpoint}
                                      className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg
                                               text-sm font-medium transition-all duration-200"
                                    >
                                      Salvar
                                    </button>
                                  </div>
                                </motion.div>
                              )}

                              {/* Campo para editar API Key dos pré-configurados (mantido para compatibilidade) */}
                              {(endpoint.nome === 'OpenRouter' || endpoint.nome === 'DeepSeek') && !endpoint.apiKey && editingEndpoint !== index && (
                                <div className="mt-4 pt-4 border-t border-white/10">
                                  <label className="block text-white/80 text-sm font-medium mb-2">
                                    Configure sua API Key:
                                  </label>
                                  <div className="flex space-x-2">
                                    <input
                                      type="password"
                                      placeholder="Cole sua API Key aqui..."
                                      className="flex-1 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg
                                               px-4 py-2 text-white placeholder-white/50 focus:outline-none
                                               focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                                      onChange={(e) => {
                                        const newKey = e.target.value;
                                        setAiEndpoints(prev => prev.map((ep, i) =>
                                          i === index ? { ...ep, apiKey: newKey } : ep
                                        ));
                                      }}
                                    />
                                    <button
                                      onClick={() => handleToggleEndpoint(index)}
                                      className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg
                                               text-sm font-medium transition-all duration-200"
                                    >
                                      Salvar
                                    </button>
                                  </div>
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    </motion.div>
                  )}

                  {activeTab === 'memoria' && (
                    <motion.div
                      key="memoria"
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -20 }}
                      className="space-y-8"
                    >
                      <div>
                        <h3 className="text-2xl font-bold text-white mb-6">Sistema de Memória</h3>

                        {/* Botões de Ação */}
                        <div className="flex flex-wrap gap-3 mb-6">
                          <button
                            onClick={() => setShowAddCategory(!showAddCategory)}
                            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg
                                     transition-all duration-200 font-medium flex items-center space-x-2"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                                d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                            </svg>
                            <span>Nova Categoria</span>
                          </button>
                          <button
                            onClick={() => setShowAddMemory(!showAddMemory)}
                            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg
                                     transition-all duration-200 font-medium flex items-center space-x-2"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                                d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                            </svg>
                            <span>Nova Memória</span>
                          </button>
                        </div>

                        {/* Formulário Nova Categoria */}
                        {showAddCategory && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            exit={{ opacity: 0, height: 0 }}
                            className="bg-white/5 border border-white/10 rounded-xl p-6 mb-6"
                          >
                            <h4 className="text-lg font-semibold text-white mb-4">Nova Categoria</h4>
                            <div className="space-y-4">
                              <div>
                                <label className="block text-white/80 text-sm font-medium mb-2">
                                  Nome da Categoria *
                                </label>
                                <input
                                  type="text"
                                  value={newCategory.nome}
                                  onChange={(e) => setNewCategory(prev => ({ ...prev, nome: e.target.value }))}
                                  className="w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg
                                           px-4 py-3 text-white placeholder-white/50 focus:outline-none
                                           focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                  placeholder="Ex: Trabalho, Pessoal, Projetos..."
                                />
                              </div>
                              <div>
                                <label className="block text-white/80 text-sm font-medium mb-2">
                                  Descrição
                                </label>
                                <textarea
                                  value={newCategory.descricao}
                                  onChange={(e) => setNewCategory(prev => ({ ...prev, descricao: e.target.value }))}
                                  className="w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg
                                           px-4 py-3 text-white placeholder-white/50 focus:outline-none
                                           focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                                  rows={3}
                                  placeholder="Descreva o propósito desta categoria..."
                                />
                              </div>
                              <div>
                                <label className="block text-white/80 text-sm font-medium mb-2">
                                  Cor da Categoria
                                </label>
                                <div className="flex space-x-2">
                                  {colors.map((color) => (
                                    <button
                                      key={color}
                                      onClick={() => setNewCategory(prev => ({ ...prev, cor: color }))}
                                      className={`w-8 h-8 rounded-full border-2 transition-all duration-200 ${
                                        newCategory.cor === color ? 'border-white scale-110' : 'border-white/30'
                                      }`}
                                      style={{ backgroundColor: color }}
                                    />
                                  ))}
                                </div>
                              </div>
                            </div>
                            <div className="flex justify-end space-x-3 mt-4">
                              <button
                                onClick={() => setShowAddCategory(false)}
                                className="px-4 py-2 text-white/70 hover:text-white transition-colors"
                              >
                                Cancelar
                              </button>
                              <button
                                onClick={handleAddCategory}
                                className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg
                                         transition-all duration-200 font-medium"
                              >
                                Criar Categoria
                              </button>
                            </div>
                          </motion.div>
                        )}

                        {/* Formulário Nova Memória */}
                        {showAddMemory && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            exit={{ opacity: 0, height: 0 }}
                            className="bg-white/5 border border-white/10 rounded-xl p-6 mb-6"
                          >
                            <h4 className="text-lg font-semibold text-white mb-4">Nova Memória</h4>
                            <div className="space-y-4">
                              <div>
                                <label className="block text-white/80 text-sm font-medium mb-2">
                                  Título da Memória *
                                </label>
                                <input
                                  type="text"
                                  value={newMemory.titulo}
                                  onChange={(e) => setNewMemory(prev => ({ ...prev, titulo: e.target.value }))}
                                  className="w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg
                                           px-4 py-3 text-white placeholder-white/50 focus:outline-none
                                           focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                  placeholder="Ex: Informações importantes sobre..."
                                />
                              </div>
                              <div>
                                <label className="block text-white/80 text-sm font-medium mb-2">
                                  Conteúdo *
                                </label>
                                <textarea
                                  value={newMemory.conteudo}
                                  onChange={(e) => setNewMemory(prev => ({ ...prev, conteudo: e.target.value }))}
                                  className="w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg
                                           px-4 py-3 text-white placeholder-white/50 focus:outline-none
                                           focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                                  rows={4}
                                  placeholder="Digite o conteúdo da memória..."
                                />
                              </div>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                  <label className="block text-white/80 text-sm font-medium mb-2">
                                    Categoria
                                  </label>
                                  <select
                                    value={newMemory.categoria || ''}
                                    onChange={(e) => setNewMemory(prev => ({ ...prev, categoria: e.target.value || null }))}
                                    className="w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg
                                             px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500
                                             focus:border-transparent"
                                  >
                                    <option value="" className="bg-gray-800">Sem categoria</option>
                                    {memoryCategories.map((category, index) => (
                                      <option key={index} value={category.nome} className="bg-gray-800">
                                        {category.nome}
                                      </option>
                                    ))}
                                  </select>
                                </div>
                                <div>
                                  <label className="block text-white/80 text-sm font-medium mb-2">
                                    Cor da Memória
                                  </label>
                                  <div className="flex space-x-2">
                                    {colors.slice(0, 4).map((color) => (
                                      <button
                                        key={color}
                                        onClick={() => setNewMemory(prev => ({ ...prev, cor: color }))}
                                        className={`w-8 h-8 rounded-full border-2 transition-all duration-200 ${
                                          newMemory.cor === color ? 'border-white scale-110' : 'border-white/30'
                                        }`}
                                        style={{ backgroundColor: color }}
                                      />
                                    ))}
                                  </div>
                                </div>
                              </div>
                              <div className="space-y-3">
                                <label className="block text-white/80 text-sm font-medium">
                                  Escopo da Memória
                                </label>
                                <select
                                  value={newMemory.global ? 'global' : (newMemory.chatId || '')}
                                  onChange={(e) => {
                                    const value = e.target.value;
                                    if (value === 'global') {
                                      setNewMemory(prev => ({ ...prev, global: true, chatId: null }));
                                    } else {
                                      setNewMemory(prev => ({ ...prev, global: false, chatId: value }));
                                    }
                                  }}
                                  className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg
                                           text-white placeholder-white/50 focus:outline-none focus:ring-2
                                           focus:ring-blue-500 focus:border-transparent"
                                >
                                  <option value="global" className="bg-gray-800 text-white">
                                    🌐 Global (todos os chats)
                                  </option>
                                  <optgroup label="Chats Específicos" className="bg-gray-800">
                                    {chats.map((chat) => (
                                      <option key={chat.id} value={chat.id} className="bg-gray-800 text-white">
                                        💬 {chat.name}
                                      </option>
                                    ))}
                                  </optgroup>
                                </select>
                                <p className="text-white/50 text-xs">
                                  {newMemory.global
                                    ? 'Esta memória ficará disponível em todos os chats'
                                    : 'Esta memória ficará disponível apenas no chat selecionado'
                                  }
                                </p>
                              </div>
                            </div>
                            <div className="flex justify-end space-x-3 mt-4">
                              <button
                                onClick={() => setShowAddMemory(false)}
                                className="px-4 py-2 text-white/70 hover:text-white transition-colors"
                              >
                                Cancelar
                              </button>
                              <button
                                onClick={handleAddMemory}
                                className="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg
                                         transition-all duration-200 font-medium"
                              >
                                Criar Memória
                              </button>
                            </div>
                          </motion.div>
                        )}

                        {/* Lista de Categorias */}
                        {memoryCategories.length > 0 && (
                          <div className="mb-6">
                            <h4 className="text-lg font-semibold text-white mb-4">Categorias</h4>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                              {memoryCategories.map((category, index) => (
                                <div key={index} className="bg-white/5 border border-white/10 rounded-xl p-4">
                                  <div className="flex items-center justify-between mb-2">
                                    <div className="flex items-center space-x-2">
                                      <div
                                        className="w-4 h-4 rounded-full"
                                        style={{ backgroundColor: category.cor }}
                                      />
                                      <h5 className="text-white font-medium">{category.nome}</h5>
                                    </div>
                                    <button
                                      onClick={() => handleDeleteCategory(index)}
                                      className="text-red-400 hover:text-red-300 transition-colors"
                                    >
                                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                                          d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                      </svg>
                                    </button>
                                  </div>
                                  {category.descricao && (
                                    <p className="text-white/60 text-sm">{category.descricao}</p>
                                  )}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Lista de Memórias */}
                        <div>
                          <h4 className="text-lg font-semibold text-white mb-4">
                            Memórias ({memories.length})
                          </h4>
                          {memories.length === 0 ? (
                            <div className="bg-white/5 border border-white/10 rounded-xl p-8 text-center">
                              <svg className="w-12 h-12 text-white/40 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                                  d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                              </svg>
                              <p className="text-white/60">Nenhuma memória criada ainda</p>
                              <p className="text-white/40 text-sm mt-1">
                                Clique em "Nova Memória" para começar
                              </p>
                            </div>
                          ) : (
                            <div className="space-y-4">
                              {memories.map((memory, index) => (
                                <div key={index} className="bg-white/5 border border-white/10 rounded-xl p-6">
                                  <div className="flex items-start justify-between mb-3">
                                    <div className="flex items-center space-x-3">
                                      <div
                                        className="w-4 h-4 rounded-full flex-shrink-0"
                                        style={{ backgroundColor: memory.cor }}
                                      />
                                      <div>
                                        <h5 className="text-white font-semibold">{memory.titulo}</h5>
                                        <div className="flex items-center space-x-2 mt-1">
                                          {memory.categoria && (
                                            <span className="bg-blue-500/20 text-blue-300 text-xs px-2 py-1 rounded-full">
                                              {memory.categoria}
                                            </span>
                                          )}
                                          <span className={`text-xs px-2 py-1 rounded-full ${
                                            memory.global
                                              ? 'bg-green-500/20 text-green-300'
                                              : 'bg-orange-500/20 text-orange-300'
                                          }`}>
                                            {memory.global
                                              ? '🌐 Global'
                                              : `💬 ${chats.find(chat => chat.id === memory.chatId)?.name || 'Chat Específico'}`
                                            }
                                          </span>
                                        </div>
                                      </div>
                                    </div>
                                    <button
                                      onClick={() => handleDeleteMemory(index)}
                                      className="text-red-400 hover:text-red-300 transition-colors"
                                    >
                                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                                          d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                      </svg>
                                    </button>
                                  </div>
                                  <p className="text-white/80 text-sm leading-relaxed">
                                    {memory.conteudo}
                                  </p>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>

            {/* Footer */}
            <div className="flex items-center justify-between p-6 border-t border-white/20">
              <div className="flex items-center space-x-3">
                <button
                  onClick={handleLogout}
                  className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg
                           transition-all duration-200 font-medium"
                >
                  Sair da Conta
                </button>


              </div>

              <div className="flex items-center space-x-3">
                <button
                  onClick={onClose}
                  className="px-6 py-2 text-white/70 hover:text-white transition-colors font-medium"
                >
                  Cancelar
                </button>
                <button
                  onClick={saveConfigurations}
                  disabled={loading}
                  className="bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed
                           text-white px-6 py-2 rounded-lg transition-all duration-200 font-medium"
                >
                  {loading ? 'Salvando...' : 'Salvar Configurações'}
                </button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
