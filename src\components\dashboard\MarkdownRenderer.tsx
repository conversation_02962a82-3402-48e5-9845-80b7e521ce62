'use client';

import React, { useMemo } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import rehypeHighlight from 'rehype-highlight';

// Importar estilos do KaTeX e Markdown
import 'katex/dist/katex.min.css';
import '@/styles/markdown-minimal.css';

interface WebSearchAnnotation {
  type: "url_citation";
  url: string;
  title: string;
  content?: string;
  start_index: number;
  end_index: number;
}

interface MarkdownRendererProps {
  content: string;
  className?: string;
  hasWebSearch?: boolean;
  webSearchAnnotations?: WebSearchAnnotation[];
  isStreaming?: boolean; // Nova prop para indicar se está em streaming
}

// Componente otimizado para renderização de markdown durante streaming
const OptimizedMarkdown = React.memo(({ content, isStreaming }: { content: string; isStreaming: boolean }) => {
  return (
    <ReactMarkdown
      remarkPlugins={[remarkGfm, remarkMath]}
      rehypePlugins={[
        rehypeKatex,
        [rehypeHighlight, { detect: true, ignoreMissing: true }]
      ]}
      components={{
        // Customizar renderização de código
        code({ node, inline, className, children, ...props }: any) {
          const match = /language-(\w+)/.exec(className || '');
          const language = match ? match[1] : 'text';

          return !inline && match ? (
            <div className="code-block-container group">
              <div className="code-header">
                <div className="flex items-center gap-2">
                  <div className="flex gap-1.5">
                    <div className="w-3 h-3 rounded-full bg-red-400/80"></div>
                    <div className="w-3 h-3 rounded-full bg-yellow-400/80"></div>
                    <div className="w-3 h-3 rounded-full bg-green-400/80"></div>
                  </div>
                  <span className="text-sm font-medium text-slate-300 capitalize">
                    {language}
                  </span>
                </div>
                <button
                  className="copy-button opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                  onClick={() => navigator.clipboard.writeText(String(children))}
                  title="Copiar código"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                </button>
              </div>
              <pre className="code-content">
                <code className={className} {...props}>
                  {children}
                </code>
              </pre>
            </div>
          ) : (
            <code className="inline-code" {...props}>
              {children}
            </code>
          );
        },

        // Customizar renderização de links
        a({ children, href, ...props }) {
          return (
            <a
              href={href}
              target="_blank"
              rel="noopener noreferrer"
              className="markdown-link"
              {...props}
            >
              {children}
            </a>
          );
        },

        // Customizar renderização de tabelas
        table({ children, ...props }) {
          return (
            <div className="table-wrapper">
              <table className="markdown-table" {...props}>
                {children}
              </table>
            </div>
          );
        },

        th({ children, ...props }) {
          return (
            <th className="table-header" {...props}>
              {children}
            </th>
          );
        },

        td({ children, ...props }) {
          return (
            <td className="table-cell" {...props}>
              {children}
            </td>
          );
        },

        // Customizar renderização de blockquotes
        blockquote({ children, ...props }) {
          return (
            <blockquote className="markdown-blockquote" {...props}>
              <div className="quote-content">
                {children}
              </div>
            </blockquote>
          );
        },

        // Customizar renderização de listas
        ul({ children, ...props }) {
          return (
            <ul className="markdown-list unordered" {...props}>
              {children}
            </ul>
          );
        },

        ol({ children, ...props }) {
          return (
            <ol className="markdown-list ordered" {...props}>
              {children}
            </ol>
          );
        },

        li({ children, ...props }) {
          return (
            <li className="list-item" {...props}>
              {children}
            </li>
          );
        },

        // Customizar renderização de títulos
        h1({ children, ...props }) {
          return (
            <h1 className="markdown-heading h1" {...props}>
              <span className="heading-content">{children}</span>
            </h1>
          );
        },

        h2({ children, ...props }) {
          return (
            <h2 className="markdown-heading h2" {...props}>
              <span className="heading-content">{children}</span>
            </h2>
          );
        },

        h3({ children, ...props }) {
          return (
            <h3 className="markdown-heading h3" {...props}>
              <span className="heading-content">{children}</span>
            </h3>
          );
        },

        h4({ children, ...props }) {
          return (
            <h4 className="markdown-heading h4" {...props}>
              <span className="heading-content">{children}</span>
            </h4>
          );
        },

        h5({ children, ...props }) {
          return (
            <h5 className="markdown-heading h5" {...props}>
              <span className="heading-content">{children}</span>
            </h5>
          );
        },

        h6({ children, ...props }) {
          return (
            <h6 className="markdown-heading h6" {...props}>
              <span className="heading-content">{children}</span>
            </h6>
          );
        },

        // Customizar renderização de parágrafos
        p({ children, ...props }) {
          return (
            <p className="markdown-paragraph" {...props}>
              {children}
            </p>
          );
        },

        // Customizar renderização de separadores
        hr({ ...props }) {
          return (
            <hr className="markdown-divider" {...props} />
          );
        }
      }}
    >
      {content}
    </ReactMarkdown>
  );
}, (prevProps, nextProps) => {
  // Durante streaming, só re-renderizar se o conteúdo mudou significativamente
  if (nextProps.isStreaming) {
    // Durante streaming, re-renderizar apenas a cada 100 caracteres para melhor performance
    const prevLength = prevProps.content.length;
    const nextLength = nextProps.content.length;
    const shouldUpdate = nextLength - prevLength >= 100 || nextLength < prevLength;
    return !shouldUpdate;
  }
  // Fora do streaming, comportamento normal
  return prevProps.content === nextProps.content;
});

const MarkdownRenderer: React.FC<MarkdownRendererProps> = React.memo(({
  content,
  className = '',
  hasWebSearch = false,
  webSearchAnnotations = [],
  isStreaming = false
}) => {
  // Função para detectar se o conteúdo contém citações de web search
  const detectWebSearch = (text: string): boolean => {
    // Detecta links no formato [dominio.com] que são característicos do web search
    const webSearchPattern = /\[[\w.-]+\.[\w]+\]/g;
    const matches = text.match(webSearchPattern);
    return matches !== null && matches.length > 0;
  };

  // Função para processar o conteúdo (OpenRouter já retorna links formatados corretamente)
  const processWebSearchLinks = (text: string): string => {
    // Como o OpenRouter já retorna os links no formato markdown correto,
    // não precisamos processar nada. Apenas retornamos o texto original.
    return text;
  };

  // Função para contar e extrair informações sobre as fontes
  const getWebSearchInfo = (text: string, annotations: WebSearchAnnotation[]): { sourceCount: number; sources: string[] } => {
    if (annotations.length > 0) {
      // Usar annotations se disponíveis
      const uniqueDomains = new Set(annotations.map(annotation => {
        try {
          return new URL(annotation.url).hostname.replace('www.', '');
        } catch (e) {
          return annotation.url;
        }
      }));

      return {
        sourceCount: annotations.length,
        sources: Array.from(uniqueDomains)
      };
    }

    // Fallback: detectar pelos padrões no texto (formato markdown link)
    const webSearchPattern = /\[[\w.-]+\.[\w]+\]\([^)]+\)/g;
    const matches = text.match(webSearchPattern) || [];
    const sourceSet = new Set(matches.map(match => {
      // Extrair o domínio do formato [dominio.com](url)
      const domainMatch = match.match(/\[([\w.-]+\.[\w]+)\]/);
      return domainMatch ? domainMatch[1] : match;
    }));
    const uniqueSources = Array.from(sourceSet);

    return {
      sourceCount: matches.length,
      sources: uniqueSources
    };
  };

  // Usar useMemo para otimizar processamento durante streaming
  const { isWebSearchMessage, webSearchInfo, processedContent } = useMemo(() => {
    const isWebSearch = hasWebSearch || detectWebSearch(content);
    const searchInfo = isWebSearch ? getWebSearchInfo(content, webSearchAnnotations) : { sourceCount: 0, sources: [] };
    const processed = isWebSearch ? processWebSearchLinks(content) : content;

    return {
      isWebSearchMessage: isWebSearch,
      webSearchInfo: searchInfo,
      processedContent: processed
    };
  }, [content, hasWebSearch, webSearchAnnotations]);

  return (
    <div className={`rafthor-markdown ${className} ${isStreaming ? 'streaming' : ''}`}>
      {/* Seção de Web Search minimalista */}
      {isWebSearchMessage && (
        <div className="web-search-indicator">
          <div className="search-badge">
            <svg className="search-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12l2 2 4-4" />
            </svg>
            <span>Busca na Web</span>
            <div className="source-count">
              {webSearchInfo.sourceCount}
            </div>
          </div>
          {webSearchInfo.sources.length > 0 && (
            <div className="source-list">
              {webSearchInfo.sources.slice(0, 3).map((source, index) => (
                <span key={index} className="source-tag">
                  {source}
                </span>
              ))}
              {webSearchInfo.sources.length > 3 && (
                <span className="source-tag more">
                  +{webSearchInfo.sources.length - 3}
                </span>
              )}
            </div>
          )}
        </div>
      )}

      {/* Conteúdo principal */}
      <OptimizedMarkdown
        content={processedContent}
        isStreaming={isStreaming}
      />
    </div>
  );
});

export default MarkdownRenderer;
