'use client';

import { AIModel } from '@/lib/types/chat';

interface ExpensiveModelConfirmationModalProps {
  isOpen: boolean;
  model: AIModel | null;
  onConfirm: () => void;
  onCancel: () => void;
}

const ExpensiveModelConfirmationModal = ({
  isOpen,
  model,
  onConfirm,
  onCancel
}: ExpensiveModelConfirmationModalProps) => {
  if (!isOpen || !model) return null;

  const promptPrice = parseFloat(model.pricing.prompt);
  const completionPrice = parseFloat(model.pricing.completion);
  const totalPrice = promptPrice + completionPrice;

  const formatPrice = (price: number) => {
    const pricePerMillion = price * 1000000; // Multiplicar por 1M para mostrar preço por 1M tokens
    if (pricePerMillion === 0) return 'Grátis';
    if (pricePerMillion < 0.001) return '< $0.001';
    return `$${pricePerMillion.toFixed(3)}`;
  };

  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-md flex items-center justify-center transition-opacity duration-300 z-[60]">
      <div className="bg-gradient-to-br from-amber-950/95 via-orange-900/95 to-red-950/95 backdrop-blur-xl rounded-2xl border border-amber-600/40 shadow-2xl w-full max-w-md mx-4 overflow-hidden relative">
        {/* Efeito de brilho sutil */}
        <div className="absolute inset-0 bg-gradient-to-br from-amber-500/10 via-transparent to-red-500/10 pointer-events-none rounded-2xl"></div>

        {/* Header com ícone de aviso */}
        <div className="p-6 border-b border-amber-700/30 relative z-10">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 rounded-full bg-gradient-to-br from-amber-500 to-orange-600 flex items-center justify-center flex-shrink-0">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <div className="flex-1">
              <h2 className="text-xl font-semibold text-amber-100">Modelo Caro Detectado</h2>
              <p className="text-amber-200/70 text-sm mt-1">Este modelo tem custos elevados</p>
            </div>
          </div>
        </div>

        {/* Conteúdo */}
        <div className="p-6 space-y-6 relative z-10">
          {/* Informações do modelo */}
          <div className="bg-amber-900/30 backdrop-blur-sm rounded-xl p-4 border border-amber-600/30">
            <h3 className="font-semibold text-amber-100 mb-2">{model.name}</h3>
            <p className="text-amber-200/80 text-sm mb-3">{model.description}</p>
            
            {/* Preços */}
            <div className="grid grid-cols-2 gap-3">
              <div className="bg-amber-800/30 rounded-lg p-3 border border-amber-600/20">
                <span className="block text-xs text-amber-300/70 font-medium mb-1">Input</span>
                <span className="text-amber-200 font-semibold">{formatPrice(promptPrice)}/1M</span>
              </div>
              <div className="bg-amber-800/30 rounded-lg p-3 border border-amber-600/20">
                <span className="block text-xs text-amber-300/70 font-medium mb-1">Output</span>
                <span className="text-amber-200 font-semibold">{formatPrice(completionPrice)}/1M</span>
              </div>
            </div>

            {/* Preço total destacado */}
            <div className="mt-4 p-3 bg-gradient-to-r from-red-900/40 to-orange-900/40 rounded-lg border border-red-600/30">
              <div className="flex items-center justify-between">
                <span className="text-red-200 font-medium">Custo Total por 1M tokens:</span>
                <span className="text-red-100 font-bold text-lg">{formatPrice(totalPrice)}</span>
              </div>
            </div>
          </div>

          {/* Aviso */}
          <div className="bg-red-900/30 backdrop-blur-sm rounded-xl p-4 border border-red-600/30">
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 rounded-full bg-red-500/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                <svg className="w-4 h-4 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="flex-1">
                <h4 className="text-red-200 font-medium mb-2">⚠️ Atenção aos Custos</h4>
                <ul className="text-red-300/80 text-sm space-y-1">
                  <li>• Este modelo tem custos superiores a $20 por milhão de tokens</li>
                  <li>• Conversas longas podem gerar custos significativos</li>
                  <li>• Monitore seu uso para evitar surpresas na fatura</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Estimativa de custo */}
          <div className="bg-blue-900/30 backdrop-blur-sm rounded-xl p-4 border border-blue-600/30">
            <h4 className="text-blue-200 font-medium mb-3">💡 Estimativa de Custos</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between text-blue-300/80">
                <span>Mensagem curta (~100 tokens):</span>
                <span className="font-medium">{formatPrice(totalPrice * 0.0001)}</span>
              </div>
              <div className="flex justify-between text-blue-300/80">
                <span>Mensagem média (~500 tokens):</span>
                <span className="font-medium">{formatPrice(totalPrice * 0.0005)}</span>
              </div>
              <div className="flex justify-between text-blue-300/80">
                <span>Mensagem longa (~2000 tokens):</span>
                <span className="font-medium">{formatPrice(totalPrice * 0.002)}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Botões */}
        <div className="p-6 border-t border-amber-700/30 flex space-x-3 relative z-10">
          <button
            onClick={onCancel}
            className="flex-1 px-6 py-3 bg-gray-700/50 hover:bg-gray-600/50 backdrop-blur-sm border border-gray-600/30 hover:border-gray-500/50 rounded-xl text-gray-200 hover:text-gray-100 transition-all duration-200 font-medium"
          >
            Cancelar
          </button>
          <button
            onClick={onConfirm}
            className="flex-1 px-6 py-3 bg-gradient-to-r from-amber-600 to-orange-600 hover:from-amber-500 hover:to-orange-500 text-white rounded-xl transition-all duration-200 font-medium hover:scale-105 shadow-lg hover:shadow-amber-500/30"
          >
            Usar Mesmo Assim
          </button>
        </div>
      </div>
    </div>
  );
};

export default ExpensiveModelConfirmationModal;
