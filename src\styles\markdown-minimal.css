/* ===== RAFTHOR MARKDOWN - DESIGN MINIMALISTA ===== */

.rafthor-markdown {
  @apply text-slate-200 leading-relaxed;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  line-height: 1.7;
  max-width: none;
}

/* ===== WEB SEARCH INDICATOR ===== */
.web-search-indicator {
  @apply mb-6 flex items-center justify-between;
  padding: 12px 16px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.08), rgba(147, 51, 234, 0.06));
  border: 1px solid rgba(59, 130, 246, 0.15);
  border-radius: 12px;
  backdrop-filter: blur(8px);
}

.search-badge {
  @apply flex items-center gap-2 text-sm font-medium text-blue-300;
}

.search-icon {
  @apply w-4 h-4;
}

.source-count {
  @apply bg-blue-500/20 text-blue-200 px-2 py-0.5 rounded-full text-xs font-semibold;
  min-width: 20px;
  text-align: center;
}

.source-list {
  @apply flex gap-2 flex-wrap;
}

.source-tag {
  @apply bg-slate-700/50 text-slate-300 px-2 py-1 rounded-md text-xs;
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.source-tag.more {
  @apply bg-blue-500/10 text-blue-300 border-blue-500/20;
}

/* ===== HEADINGS ===== */
.markdown-heading {
  @apply font-semibold text-white mb-4 mt-8 first:mt-0;
  letter-spacing: -0.025em;
}

.markdown-heading .heading-content {
  position: relative;
}

.markdown-heading.h1 {
  @apply text-3xl mb-6;
  border-bottom: 1px solid rgba(148, 163, 184, 0.15);
  padding-bottom: 12px;
}

.markdown-heading.h2 {
  @apply text-2xl;
}

.markdown-heading.h3 {
  @apply text-xl;
}

.markdown-heading.h4 {
  @apply text-lg;
}

.markdown-heading.h5 {
  @apply text-base;
}

.markdown-heading.h6 {
  @apply text-sm uppercase tracking-wider text-slate-400;
}

/* ===== PARAGRAPHS ===== */
.markdown-paragraph {
  @apply mb-4 text-slate-200;
  line-height: 1.75;
}

.markdown-paragraph:last-child {
  @apply mb-0;
}

/* ===== LINKS ===== */
.markdown-link {
  @apply text-blue-400 no-underline relative;
  transition: all 0.2s ease;
}

.markdown-link:hover {
  @apply text-blue-300;
}

.markdown-link::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 0;
  height: 1px;
  background: currentColor;
  transition: width 0.2s ease;
}

.markdown-link:hover::after {
  width: 100%;
}

/* ===== CODE BLOCKS ===== */
.code-block-container {
  @apply mb-6 rounded-xl overflow-hidden;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95), rgba(30, 41, 59, 0.9));
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.code-header {
  @apply flex items-center justify-between px-4 py-3;
  background: rgba(15, 23, 42, 0.8);
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.copy-button {
  @apply text-slate-400 hover:text-slate-200 p-1.5 rounded-md hover:bg-slate-700/50;
  transition: all 0.2s ease;
}

.code-content {
  @apply p-4 overflow-x-auto;
  font-family: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
  font-size: 14px;
  line-height: 1.5;
}

.code-content code {
  @apply text-slate-200;
  background: none !important;
}

.inline-code {
  @apply bg-slate-700/50 text-slate-200 px-2 py-0.5 rounded-md text-sm;
  font-family: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
  border: 1px solid rgba(148, 163, 184, 0.1);
}

/* ===== BLOCKQUOTES ===== */
.markdown-blockquote {
  @apply my-6 pl-6 relative;
}

.markdown-blockquote::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(to bottom, #3b82f6, #8b5cf6);
  border-radius: 2px;
}

.quote-content {
  @apply text-slate-300 italic;
  font-size: 1.05em;
  line-height: 1.6;
}

/* ===== LISTS ===== */
.markdown-list {
  @apply mb-4 space-y-2;
}

.markdown-list.unordered {
  @apply list-none;
}

.markdown-list.ordered {
  @apply list-none;
  counter-reset: list-counter;
}

.list-item {
  @apply relative pl-6 text-slate-200;
  line-height: 1.6;
}

.unordered .list-item::before {
  content: '';
  position: absolute;
  left: 8px;
  top: 12px;
  width: 4px;
  height: 4px;
  background: #3b82f6;
  border-radius: 50%;
}

.ordered .list-item {
  counter-increment: list-counter;
}

.ordered .list-item::before {
  content: counter(list-counter);
  position: absolute;
  left: 0;
  top: 0;
  @apply text-blue-400 font-medium text-sm;
  min-width: 20px;
}

/* ===== TABLES ===== */
.table-wrapper {
  @apply my-6 overflow-x-auto rounded-lg;
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.markdown-table {
  @apply w-full border-collapse;
  background: rgba(15, 23, 42, 0.5);
}

.table-header {
  @apply px-4 py-3 text-left font-semibold text-white;
  background: rgba(30, 41, 59, 0.8);
  border-bottom: 1px solid rgba(148, 163, 184, 0.15);
}

.table-cell {
  @apply px-4 py-3 text-slate-200;
  border-bottom: 1px solid rgba(148, 163, 184, 0.08);
}

.markdown-table tr:last-child .table-cell {
  border-bottom: none;
}

/* ===== DIVIDER ===== */
.markdown-divider {
  @apply my-8 border-0 h-px;
  background: linear-gradient(to right, transparent, rgba(148, 163, 184, 0.3), transparent);
}

/* ===== RESPONSIVE ===== */
@media (max-width: 640px) {
  .rafthor-markdown {
    font-size: 15px;
  }
  
  .markdown-heading.h1 {
    @apply text-2xl;
  }
  
  .markdown-heading.h2 {
    @apply text-xl;
  }
  
  .code-header {
    @apply px-3 py-2;
  }
  
  .code-content {
    @apply p-3;
    font-size: 13px;
  }
  
  .web-search-indicator {
    @apply flex-col items-start gap-3;
  }
  
  .source-list {
    @apply w-full;
  }
}

/* ===== STREAMING MODE ===== */
.rafthor-markdown.streaming * {
  animation: none !important;
  transition: none !important;
}

/* ===== FÓRMULAS MATEMÁTICAS (KATEX) ===== */

/* Remover estilos padrão do KaTeX que causam dupla caixa */
.rafthor-markdown .katex-display > .katex {
  background: none !important;
  border: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* Fórmulas em bloco - Uma única caixa elegante */
.rafthor-markdown .katex-display {
  @apply my-8 overflow-x-auto;
  padding: 28px 24px;
  background: rgba(15, 23, 42, 0.4);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 12px;
  text-align: center;
  transition: all 0.3s ease;
}

.rafthor-markdown .katex-display:hover {
  background: rgba(15, 23, 42, 0.6);
  border-color: rgba(148, 163, 184, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.rafthor-markdown .katex-display .katex {
  color: #f1f5f9 !important;
  font-size: 1.25em !important;
  display: block;
}

/* Fórmulas inline - Estilo minimalista */
.rafthor-markdown p .katex {
  color: #e2e8f0 !important;
  background: rgba(148, 163, 184, 0.08);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 1.05em !important;
  border: 1px solid rgba(148, 163, 184, 0.05);
  transition: all 0.2s ease;
}

.rafthor-markdown p .katex:hover {
  background: rgba(148, 163, 184, 0.12);
  border-color: rgba(148, 163, 184, 0.15);
}

/* Cores consistentes para todos os elementos matemáticos */
.rafthor-markdown .katex .mord,
.rafthor-markdown .katex .mbin,
.rafthor-markdown .katex .mrel,
.rafthor-markdown .katex .mopen,
.rafthor-markdown .katex .mclose,
.rafthor-markdown .katex .mpunct,
.rafthor-markdown .katex .mop,
.rafthor-markdown .katex .mspace,
.rafthor-markdown .katex .base {
  color: inherit !important;
}

/* Linhas de fração e raiz com cor consistente */
.rafthor-markdown .katex .mfrac .frac-line {
  border-bottom-color: currentColor !important;
  opacity: 0.8;
}

.rafthor-markdown .katex .sqrt .sqrt-line {
  border-top-color: currentColor !important;
  opacity: 0.8;
}

/* Responsividade */
@media (max-width: 640px) {
  .rafthor-markdown .katex-display {
    padding: 20px 16px;
    margin: 20px 0;
  }

  .rafthor-markdown .katex-display .katex {
    font-size: 1.1em !important;
  }

  .rafthor-markdown p .katex {
    font-size: 1em !important;
  }
}

/* ===== SYNTAX HIGHLIGHTING OVERRIDES ===== */
.code-content .hljs-keyword { color: #c792ea; }
.code-content .hljs-string { color: #c3e88d; }
.code-content .hljs-number { color: #f78c6c; }
.code-content .hljs-comment { color: #546e7a; font-style: italic; }
.code-content .hljs-function { color: #82aaff; }
.code-content .hljs-variable { color: #eeffff; }
.code-content .hljs-title { color: #ffcb6b; }
.code-content .hljs-attr { color: #c792ea; }
.code-content .hljs-built_in { color: #ffcb6b; }
.code-content .hljs-type { color: #c792ea; }
.code-content .hljs-literal { color: #ff5370; }
.code-content .hljs-meta { color: #546e7a; }
.code-content .hljs-tag { color: #f07178; }
.code-content .hljs-name { color: #f07178; }
.code-content .hljs-selector-id,
.code-content .hljs-selector-class { color: #ffcb6b; }
