// Interfaces para o sistema de estatísticas

export interface WordFrequency {
  word: string;
  count: number;
  percentage: number;
}

export interface MessageStatistics {
  totalMessages: number;
  totalWords: number;
  totalWordsAI: number;
  totalWordsUser: number;
  totalPromptTokens: number;
  totalCompletionTokens: number;
  totalCost: number;
  averageResponseTime: number;
  averageMessageLength: number;
  averageWordsPerMessage: number;
  averageSentencesPerMessage: number;
  estimatedReadingTime: number; // em minutos
  totalCharactersAI: number;
  totalCharactersUser: number;
  totalCharacters: number;
  mostUsedWords: WordFrequency[];
}

export interface DetailedStatistics extends MessageStatistics {
  // Estatísticas por período
  dailyStats: DailyStats[];
  weeklyStats: WeeklyStats[];
  monthlyStats: MonthlyStats[];
  
  // Estatísticas por modelo
  modelStats: ModelStats[];
  
  // Estatísticas de anexos
  attachmentStats: AttachmentStats;
  
  // Estatísticas de tempo
  timeStats: TimeStats;
  
  // Estatísticas de favoritos
  favoriteStats: FavoriteStats;
}

export interface DailyStats {
  date: string; // YYYY-MM-DD
  messages: number;
  words: number;
  tokens: number;
  cost: number;
  averageResponseTime: number;
}

export interface WeeklyStats {
  week: string; // YYYY-WW
  messages: number;
  words: number;
  tokens: number;
  cost: number;
  averageResponseTime: number;
}

export interface MonthlyStats {
  month: string; // YYYY-MM
  messages: number;
  words: number;
  tokens: number;
  cost: number;
  averageResponseTime: number;
}

export interface ModelStats {
  modelId: string;
  modelName: string;
  messagesCount: number;
  tokensUsed: number;
  totalCost: number;
  averageResponseTime: number;
  percentage: number;
}

export interface AttachmentStats {
  totalAttachments: number;
  imageAttachments: number;
  pdfAttachments: number;
  totalSize: number; // em bytes
  averageSize: number; // em bytes
  attachmentsByType: {
    [key: string]: number;
  };
}

export interface TimeStats {
  totalSessionTime: number; // em ms
  averageSessionTime: number; // em ms
  longestSession: number; // em ms
  shortestSession: number; // em ms
  sessionsCount: number;
  mostActiveHour: number; // 0-23
  mostActiveDay: string; // 'monday', 'tuesday', etc.
  timeDistribution: {
    [hour: number]: number; // mensagens por hora
  };
}

export interface FavoriteStats {
  totalFavorites: number;
  favoritesByRole: {
    user: number;
    assistant: number;
  };
  averageFavoriteLength: number;
  mostFavoritedWords: WordFrequency[];
}

export interface StatisticsFilters {
  dateRange?: {
    start: Date;
    end: Date;
  };
  chatIds?: string[];
  modelIds?: string[];
  includeAttachments?: boolean;
}

export interface StatisticsExport {
  format: 'json' | 'csv' | 'pdf';
  data: DetailedStatistics;
  generatedAt: number;
  filters?: StatisticsFilters;
}

// Tipos para componentes de visualização
export interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string | string[];
    borderColor?: string | string[];
    borderWidth?: number;
  }[];
}

export interface StatCard {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: string;
  color: 'blue' | 'green' | 'purple' | 'orange' | 'red' | 'cyan';
  trend?: {
    value: number;
    isPositive: boolean;
  };
}
