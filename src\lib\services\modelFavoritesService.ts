import { doc, setDoc, deleteDoc, getDoc, collection, getDocs } from 'firebase/firestore';
import { db } from '@/lib/firebase';

interface FavoriteModel {
  modelId: string;
  modelName: string;
  endpointId: string;
  addedAt: number;
}

class ModelFavoritesService {
  // Função para escapar IDs que contêm caracteres especiais
  private escapeModelId(modelId: string): string {
    return modelId.replace(/\//g, '_SLASH_').replace(/\./g, '_DOT_');
  }

  // Função para desescapar IDs
  private unescapeModelId(escapedId: string): string {
    return escapedId.replace(/_SLASH_/g, '/').replace(/_DOT_/g, '.');
  }

  // Adicionar modelo aos favoritos
  async addToFavorites(userId: string, endpointId: string, modelId: string, modelName: string): Promise<void> {
    try {
      const escapedModelId = this.escapeModelId(modelId);
      const favoriteRef = doc(db, 'usuarios', userId, 'endpoints', endpointId, 'modelos_favoritos', escapedModelId);
      const favoriteData: FavoriteModel = {
        modelId,
        modelName,
        endpointId,
        addedAt: Date.now()
      };

      await setDoc(favoriteRef, favoriteData);
      console.log('Model added to favorites successfully');
    } catch (error) {
      console.error('Error adding model to favorites:', error);
      throw error;
    }
  }

  // Remover modelo dos favoritos
  async removeFromFavorites(userId: string, endpointId: string, modelId: string): Promise<void> {
    try {
      const escapedModelId = this.escapeModelId(modelId);
      const favoriteRef = doc(db, 'usuarios', userId, 'endpoints', endpointId, 'modelos_favoritos', escapedModelId);
      await deleteDoc(favoriteRef);
      console.log('Model removed from favorites successfully');
    } catch (error) {
      console.error('Error removing model from favorites:', error);
      throw error;
    }
  }

  // Verificar se um modelo é favorito
  async isModelFavorited(userId: string, endpointId: string, modelId: string): Promise<boolean> {
    try {
      const escapedModelId = this.escapeModelId(modelId);
      const favoriteRef = doc(db, 'usuarios', userId, 'endpoints', endpointId, 'modelos_favoritos', escapedModelId);
      const favoriteDoc = await getDoc(favoriteRef);
      return favoriteDoc.exists();
    } catch (error) {
      console.error('Error checking if model is favorited:', error);
      return false;
    }
  }

  // Alternar status de favorito (adicionar se não existe, remover se existe)
  async toggleFavorite(userId: string, endpointId: string, modelId: string, modelName: string): Promise<boolean> {
    try {
      const isFavorited = await this.isModelFavorited(userId, endpointId, modelId);
      
      if (isFavorited) {
        await this.removeFromFavorites(userId, endpointId, modelId);
        return false;
      } else {
        await this.addToFavorites(userId, endpointId, modelId, modelName);
        return true;
      }
    } catch (error) {
      console.error('Error toggling favorite:', error);
      throw error;
    }
  }

  // Obter todos os modelos favoritos de um endpoint
  async getFavoriteModels(userId: string, endpointId: string): Promise<FavoriteModel[]> {
    try {
      const favoritesRef = collection(db, 'usuarios', userId, 'endpoints', endpointId, 'modelos_favoritos');
      const favoritesSnapshot = await getDocs(favoritesRef);

      const favorites: FavoriteModel[] = [];
      favoritesSnapshot.forEach(doc => {
        const data = doc.data() as FavoriteModel;
        // Desescapar o modelId se necessário
        data.modelId = this.unescapeModelId(data.modelId);
        favorites.push(data);
      });

      // Ordenar por data de adição (mais recente primeiro)
      favorites.sort((a, b) => b.addedAt - a.addedAt);

      return favorites;
    } catch (error) {
      console.error('Error getting favorite models:', error);
      throw error;
    }
  }

  // Obter apenas os IDs dos modelos favoritos (para performance)
  async getFavoriteModelIds(userId: string, endpointId: string): Promise<Set<string>> {
    try {
      const favorites = await this.getFavoriteModels(userId, endpointId);
      // Os IDs já estão desescapados pela função getFavoriteModels
      return new Set(favorites.map(fav => fav.modelId));
    } catch (error) {
      console.error('Error getting favorite model IDs:', error);
      return new Set();
    }
  }

  // Obter estatísticas dos favoritos
  async getFavoritesStats(userId: string, endpointId: string): Promise<{
    totalFavorites: number;
    mostRecentlyAdded?: FavoriteModel;
    oldestFavorite?: FavoriteModel;
  }> {
    try {
      const favorites = await this.getFavoriteModels(userId, endpointId);
      
      if (favorites.length === 0) {
        return { totalFavorites: 0 };
      }

      const sortedByDate = [...favorites].sort((a, b) => b.addedAt - a.addedAt);
      
      return {
        totalFavorites: favorites.length,
        mostRecentlyAdded: sortedByDate[0],
        oldestFavorite: sortedByDate[sortedByDate.length - 1]
      };
    } catch (error) {
      console.error('Error getting favorites stats:', error);
      return { totalFavorites: 0 };
    }
  }

  // Limpar todos os favoritos de um endpoint
  async clearAllFavorites(userId: string, endpointId: string): Promise<void> {
    try {
      const favorites = await this.getFavoriteModels(userId, endpointId);

      // Os IDs já estão desescapados, então a função removeFromFavorites vai escapá-los novamente
      const deletePromises = favorites.map(favorite =>
        this.removeFromFavorites(userId, endpointId, favorite.modelId)
      );

      await Promise.all(deletePromises);
      console.log('All favorites cleared successfully');
    } catch (error) {
      console.error('Error clearing all favorites:', error);
      throw error;
    }
  }

  // Exportar favoritos para backup
  async exportFavorites(userId: string, endpointId: string): Promise<FavoriteModel[]> {
    try {
      return await this.getFavoriteModels(userId, endpointId);
    } catch (error) {
      console.error('Error exporting favorites:', error);
      throw error;
    }
  }

  // Importar favoritos de backup
  async importFavorites(userId: string, endpointId: string, favorites: FavoriteModel[]): Promise<void> {
    try {
      const importPromises = favorites.map(favorite => 
        this.addToFavorites(userId, endpointId, favorite.modelId, favorite.modelName)
      );

      await Promise.all(importPromises);
      console.log('Favorites imported successfully');
    } catch (error) {
      console.error('Error importing favorites:', error);
      throw error;
    }
  }
}

export const modelFavoritesService = new ModelFavoritesService();
