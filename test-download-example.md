# Exemplo de Teste para Download do Chat

Este arquivo demonstra como o conteúdo será renderizado no HTML exportado do chat.

## Elementos de Markdown

### Texto Formatado
- **Texto em negrito**
- *Texto em itálico*
- ~~Texto riscado~~
- `código inline`

### Listas
1. Primeiro item
2. Segundo item
   - Sub-item A
   - Sub-item B
3. Terceiro item

### Citações
> Esta é uma citação elegante que será renderizada com o tema azul do Rafthor.

### Código
```javascript
function fibonacci(n) {
  if (n <= 1) return n;
  return fibonacci(n - 1) + fi<PERSON><PERSON><PERSON>(n - 2);
}

console.log(fibon<PERSON>ci(10)); // 55
```

```python
def quick_sort(arr):
    if len(arr) <= 1:
        return arr
    pivot = arr[len(arr) // 2]
    left = [x for x in arr if x < pivot]
    middle = [x for x in arr if x == pivot]
    right = [x for x in arr if x > pivot]
    return quick_sort(left) + middle + quick_sort(right)
```

### Tabela
| Linguagem | Tipo | Dificuldade |
|-----------|------|-------------|
| JavaScript | Interpretada | Média |
| Python | Interpretada | Baixa |
| Rust | Compilada | Alta |

### Links
Visite o [site do Rafthor](https://rafthor.com) para mais informações.

---

## Fórmulas Matemáticas (LaTeX)

### Fórmulas Inline
A área de um círculo é $A = \pi r^2$ onde $r$ é o raio.

A fórmula quadrática é $x = \frac{-b \pm \sqrt{b^2 - 4ac}}{2a}$.

### Fórmulas em Bloco
$$
\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}
$$

$$
\sum_{n=1}^{\infty} \frac{1}{n^2} = \frac{\pi^2}{6}
$$

$$
\begin{pmatrix}
a & b \\
c & d
\end{pmatrix}
\begin{pmatrix}
x \\
y
\end{pmatrix}
=
\begin{pmatrix}
ax + by \\
cx + dy
\end{pmatrix}
$$

### Equações Complexas
$$
f(x) = \begin{cases}
x^2 & \text{se } x \geq 0 \\
-x^2 & \text{se } x < 0
\end{cases}
$$

$$
\lim_{n \to \infty} \left(1 + \frac{1}{n}\right)^n = e
$$

---

## Resultado Esperado

Quando este conteúdo for exportado via modal de download:

1. ✅ **Markdown será processado** - Todos os elementos de markdown serão convertidos para HTML estilizado
2. ✅ **LaTeX será renderizado** - Fórmulas matemáticas serão renderizadas com KaTeX
3. ✅ **Código será destacado** - Syntax highlighting com Highlight.js
4. ✅ **Estilos profissionais** - Tema azul consistente com o Rafthor
5. ✅ **Responsivo** - Funciona em dispositivos móveis
6. ✅ **Standalone** - HTML completo com todas as dependências

O arquivo HTML resultante será totalmente independente e poderá ser aberto em qualquer navegador mantendo toda a formatação e funcionalidade.
