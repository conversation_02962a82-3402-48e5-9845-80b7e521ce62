'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { doc, setDoc, updateDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';

interface CreateFolderModalProps {
  isOpen: boolean;
  onClose: () => void;
  username: string;
  onFolderCreated: (folderId: string) => void;
  editingFolder?: {
    id: string;
    name: string;
    description?: string;
    color: string;
    expandedByDefault: boolean;
  } | null;
}

interface FolderData {
  name: string;
  description: string;
  color: string;
  expandedByDefault: boolean;
}

const FOLDER_COLORS = [
  { name: 'Azul', value: 'blue', bg: 'bg-blue-500', selected: 'bg-blue-600' },
  { name: 'Verde', value: 'green', bg: 'bg-green-500', selected: 'bg-green-600' },
  { name: '<PERSON><PERSON>', value: 'yellow', bg: 'bg-yellow-500', selected: 'bg-yellow-600' },
  { name: 'Vermel<PERSON>', value: 'red', bg: 'bg-red-500', selected: 'bg-red-600' },
  { name: 'Roxo', value: 'purple', bg: 'bg-purple-500', selected: 'bg-purple-600' },
  { name: 'Ciano', value: 'cyan', bg: 'bg-cyan-500', selected: 'bg-cyan-600' },
  { name: 'Lima', value: 'lime', bg: 'bg-lime-500', selected: 'bg-lime-600' },
  { name: 'Laranja', value: 'orange', bg: 'bg-orange-500', selected: 'bg-orange-600' },
  { name: 'Rosa', value: 'pink', bg: 'bg-pink-500', selected: 'bg-pink-600' },
  { name: 'Cinza', value: 'gray', bg: 'bg-gray-500', selected: 'bg-gray-600' },
];

export default function CreateFolderModal({
  isOpen,
  onClose,
  username,
  onFolderCreated,
  editingFolder = null
}: CreateFolderModalProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const [folderData, setFolderData] = useState<FolderData>({
    name: '',
    description: '',
    color: 'blue',
    expandedByDefault: true
  });

  // Carregar dados quando estiver editando
  useEffect(() => {
    if (editingFolder) {
      setFolderData({
        name: editingFolder.name,
        description: editingFolder.description || '',
        color: editingFolder.color,
        expandedByDefault: editingFolder.expandedByDefault
      });
    } else {
      // Reset para criar nova pasta
      setFolderData({
        name: '',
        description: '',
        color: 'blue',
        expandedByDefault: true
      });
    }
  }, [editingFolder]);

  const handleInputChange = (field: keyof FolderData, value: string | boolean) => {
    setFolderData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const generateFolderId = () => {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `folder_${timestamp}_${random}`;
  };

  const createFolder = async () => {
    if (!folderData.name.trim()) {
      setError('Nome da pasta é obrigatório');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const now = new Date().toISOString();

      if (editingFolder) {
        // Editando pasta existente
        const firestoreData = {
          name: folderData.name,
          description: folderData.description,
          color: folderData.color,
          expandedByDefault: folderData.expandedByDefault,
          updatedAt: now
        };

        await updateDoc(doc(db, 'usuarios', username, 'pastas', editingFolder.id), firestoreData);
        console.log('Pasta atualizada com sucesso:', editingFolder.id);
        onFolderCreated(editingFolder.id);
      } else {
        // Criando nova pasta
        const folderId = generateFolderId();
        const firestoreData = {
          name: folderData.name,
          description: folderData.description,
          color: folderData.color,
          expandedByDefault: folderData.expandedByDefault,
          createdAt: now,
          updatedAt: now,
          chatCount: 0
        };

        await setDoc(doc(db, 'usuarios', username, 'pastas', folderId), firestoreData);
        console.log('Pasta criada com sucesso:', folderId);
        onFolderCreated(folderId);
      }

      onClose();

      // Reset form apenas se não estiver editando
      if (!editingFolder) {
        setFolderData({
          name: '',
          description: '',
          color: 'blue',
          expandedByDefault: true
        });
      }

    } catch (error) {
      console.error('Erro ao salvar pasta:', error);
      setError(editingFolder ? 'Erro ao atualizar pasta. Tente novamente.' : 'Erro ao criar pasta. Tente novamente.');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        {/* Background */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="absolute inset-0 bg-gradient-to-br from-black/40 via-blue-900/30 to-purple-900/40 backdrop-blur-xl"
        />

        <motion.div
          initial={{ scale: 0.8, opacity: 0, y: 50 }}
          animate={{ scale: 1, opacity: 1, y: 0 }}
          exit={{ scale: 0.8, opacity: 0, y: 50 }}
          transition={{ 
            type: "spring", 
            duration: 0.6,
            bounce: 0.3
          }}
          className="relative bg-gradient-to-br from-slate-900/90 via-blue-900/90 to-indigo-900/90 backdrop-blur-2xl
                     border border-white/20 rounded-3xl w-full max-w-md overflow-hidden
                     shadow-2xl shadow-blue-900/50"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="relative p-6 border-b border-white/10 bg-gradient-to-r from-white/5 to-transparent">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center shadow-lg shadow-blue-500/30">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                        d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
                </svg>
              </div>
              <div>
                <h2 className="text-xl font-bold text-white">
                  {editingFolder ? 'Editar Pasta' : 'Nova Pasta'}
                </h2>
              </div>
            </div>
            <button
              onClick={onClose}
              className="absolute top-4 right-4 text-white/60 hover:text-white transition-all duration-300
                       p-2 hover:bg-white/10 rounded-xl"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Content */}
          <div className="p-6 space-y-5">
            {/* Nome da pasta */}
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <label className="text-sm font-medium text-blue-300 flex items-center gap-2">
                  <svg className="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                  </svg>
                  Nome da pasta
                </label>
                <span className="text-red-400 text-xs bg-red-500/20 px-2 py-1 rounded-full">
                  obrigatório
                </span>
              </div>
              <input
                type="text"
                value={folderData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Digite o nome da pasta"
                className="w-full bg-white/10 border border-white/20 rounded-2xl px-4 py-3 text-white
                         placeholder-white/40 focus:outline-none focus:border-blue-400 focus:bg-white/15
                         transition-all duration-300 hover:bg-white/15"
              />
              <p className="text-white/60 text-xs">
                Nome para identificar a pasta de chats
              </p>
            </div>

            {/* Descrição */}
            <div className="space-y-3">
              <label className="text-sm font-medium text-blue-300 flex items-center gap-2">
                <svg className="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Descrição
              </label>
              <textarea
                value={folderData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Descrição opcional da pasta"
                rows={2}
                className="w-full bg-white/10 border border-white/20 rounded-2xl px-4 py-3 text-white
                         placeholder-white/40 focus:outline-none focus:border-blue-400 focus:bg-white/15
                         transition-all duration-300 hover:bg-white/15 resize-none"
              />
              <p className="text-white/60 text-xs">
                Descrição opcional para a pasta
              </p>
            </div>

            {/* Cor da pasta */}
            <div className="space-y-3">
              <label className="text-sm font-medium text-blue-300">Cor da pasta</label>
              <div className="grid grid-cols-5 gap-2">
                {FOLDER_COLORS.map((color) => (
                  <button
                    key={color.value}
                    onClick={() => handleInputChange('color', color.value)}
                    className={`w-10 h-10 rounded-xl transition-all duration-200 ${
                      folderData.color === color.value 
                        ? `${color.selected} ring-2 ring-white/50 scale-110` 
                        : `${color.bg} hover:scale-105`
                    }`}
                    title={color.name}
                  >
                    {folderData.color === color.value && (
                      <svg className="w-5 h-5 text-white mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    )}
                  </button>
                ))}
              </div>
              <p className="text-white/60 text-xs">
                Escolha uma cor para identificar visualmente a pasta
              </p>
            </div>

            {/* Expandida por padrão */}
            <div className="space-y-3">
              <div className="flex items-center justify-between p-4 bg-white/5 rounded-2xl border border-white/10">
                <div>
                  <h3 className="text-sm font-medium text-blue-300">Expandida por padrão</h3>
                  <p className="text-white/60 text-xs mt-1">A pasta ficará aberta quando você acessar o chat</p>
                </div>
                <button
                  onClick={() => handleInputChange('expandedByDefault', !folderData.expandedByDefault)}
                  className={`relative w-14 h-7 rounded-full transition-all duration-300 ${
                    folderData.expandedByDefault 
                      ? 'bg-gradient-to-r from-blue-500 to-cyan-500' 
                      : 'bg-white/20'
                  }`}
                >
                  <div 
                    className={`absolute top-1 w-5 h-5 bg-white rounded-full shadow-lg transition-all duration-300 ${
                      folderData.expandedByDefault ? 'left-8' : 'left-1'
                    }`}
                  />
                </button>
              </div>
            </div>

            {/* Error Message */}
            {error && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="p-3 bg-red-500/20 border border-red-500/30 rounded-xl text-red-300 text-sm"
              >
                {error}
              </motion.div>
            )}
          </div>

          {/* Footer */}
          <div className="p-6 border-t border-white/10 bg-white/5">
            <div className="flex justify-end space-x-3">
              <button
                onClick={onClose}
                disabled={loading}
                className="px-6 py-3 text-white/70 hover:text-white transition-all duration-300
                         hover:bg-white/10 rounded-xl disabled:opacity-50 font-medium"
              >
                Cancelar
              </button>
              <button
                onClick={createFolder}
                disabled={loading || !folderData.name.trim()}
                className="px-8 py-3 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600
                         text-white rounded-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed
                         flex items-center space-x-2 font-medium shadow-lg shadow-blue-500/25 hover:shadow-blue-500/40"
              >
                {loading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                    <span>{editingFolder ? 'Salvando...' : 'Criando...'}</span>
                  </>
                ) : (
                  <>
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                            d={editingFolder ? "M5 13l4 4L19 7" : "M12 4v16m8-8H4"} />
                    </svg>
                    <span>{editingFolder ? 'Salvar Alterações' : 'Criar Pasta'}</span>
                  </>
                )}
              </button>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}
