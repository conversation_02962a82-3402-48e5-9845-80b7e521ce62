import { NextRequest, NextResponse } from 'next/server';
import { ref, getDownloadURL } from 'firebase/storage';
import { storage } from '@/lib/firebase';

export async function GET(
  request: NextRequest,
  { params }: { params: { username: string; chatId: string } }
) {
  try {
    const { username, chatId } = params;

    // Validar parâmetros
    if (!username || !chatId) {
      return NextResponse.json(
        { error: 'Username e chatId são obrigatórios' },
        { status: 400 }
      );
    }

    // Referência para o arquivo chat.json no Firebase Storage
    const chatJsonRef = ref(storage, `usuarios/${username}/conversas/${chatId}/chat.json`);
    
    try {
      // Obter URL de download
      const downloadUrl = await getDownloadURL(chatJsonRef);
      
      // Fazer fetch do arquivo
      const response = await fetch(downloadUrl);
      
      if (!response.ok) {
        throw new Error(`Erro ao buscar arquivo: ${response.statusText}`);
      }

      const chatData = await response.json();

      // Retornar dados com headers CORS apropriados
      return NextResponse.json(chatData, {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        },
      });

    } catch (storageError) {
      console.error('Erro ao acessar Firebase Storage:', storageError);
      
      // Se o arquivo não existe, retornar estrutura vazia
      const emptyChatData = {
        id: chatId,
        name: 'Chat',
        messages: [],
        createdAt: new Date().toISOString(),
        lastUpdated: new Date().toISOString(),
      };

      return NextResponse.json(emptyChatData, {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        },
      });
    }

  } catch (error) {
    console.error('Erro na API de chat:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { 
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        },
      }
    );
  }
}

// Suporte para preflight requests (OPTIONS)
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
